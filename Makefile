.PHONY: upload install build clean dev lint preview typecheck deps sync-add sync-pull sync-push

# Development server
dev:
	npm run dev

# Build the project
build:
	npm run build

# Type checking
typecheck:
	tsc -b

# Lint code
lint:
	npm run lint

# Lint and fix issues
lint-fix:
	npm run lint -- --fix

# Preview production build
preview: build
	npm run preview

# Install dependencies
deps:
	npm install

# <NAME_EMAIL>:/opt/web
upload: build
	ssh <EMAIL> "test -d /opt/web && rm -rf /opt/web/* || echo 'Directory /opt/web does not exist'"
	scp -r ./dist/* <EMAIL>:/opt/web/

# <NAME_EMAIL> development environment
install: build
	ssh <EMAIL> "test -d /home/<USER>/Work/prj_SonarMPSoC/petalinux/prj_se_emmc/project-spec/meta-user/recipes-apps/sonar-apps/files/opt/web && rm -rf /home/<USER>/Work/prj_SonarMPSoC/petalinux/prj_se_emmc/project-spec/meta-user/recipes-apps/sonar-apps/files/opt/web/* || echo 'Directory does not exist'"
	scp -r ./dist/* <EMAIL>:/home/<USER>/Work/prj_SonarMPSoC/petalinux/prj_se_emmc/project-spec/meta-user/recipes-apps/sonar-apps/files/opt/web/

# Check before upload (lint + typecheck + build)
check: lint typecheck build
	@echo "All checks passed!"

# Clean build artifacts
clean:
	rm -rf dist

# Full clean (including node_modules)
clean-all: clean
	rm -rf node_modules package-lock.json

# Git Subtree synchronization targets
REMOTE_URL := **************:Selladore-Wong/hj-sonar-react-components.git
PREFIX := sonar-react-components

# Add component library as subtree
sync-add:
	@echo "添加组件库作为 subtree..."
	git subtree add --prefix=$(PREFIX) $(REMOTE_URL) main --squash

# Pull updates from component library
sync-pull:
	@echo "从组件库仓库拉取更新..."
	git subtree pull --prefix=$(PREFIX) $(REMOTE_URL) main --squash

# Push changes to component library
sync-push:
	@echo "推送更改到组件库仓库..."
	git subtree push --prefix=$(PREFIX) $(REMOTE_URL) main

# Help
help:
	@echo "Available targets:"
	@echo "  dev       - Start development server"
	@echo "  build     - Build the project"
	@echo "  typecheck - Run TypeScript type checking"
	@echo "  lint      - Run ESLint"
	@echo "  lint-fix  - Run ESLint with auto-fix"
	@echo "  preview   - Preview production build"
	@echo "  deps      - Install dependencies"
	@echo "  check     - Run lint + typecheck + build"
	@echo "  upload    - Build and <NAME_EMAIL>:/opt/web"
	@echo "  install   - Build and <NAME_EMAIL> development environment"
	@echo "  clean     - Remove dist directory"
	@echo "  clean-all - Remove dist and node_modules"
	@echo "  sync-add  - Add component library as subtree"
	@echo "  sync-pull - Pull updates from component library"
	@echo "  sync-push - Push changes to component library"
	@echo "  help      - Show this help message"