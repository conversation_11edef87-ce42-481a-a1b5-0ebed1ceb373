import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Info, Download } from 'lucide-react';

export interface VersionInfo {
  FPGA: string;
  Embedded: string;
}

export interface DeviceInfo {
  deviceId: number;
  partNumber: number;
  platform: string;
  status: number;
  timestamp: string;
  unixTimestamp: number;
}

export interface SonarDeviceInfoProps {
  sonarIP: string;
  onDeviceInfoChange?: (deviceInfo: DeviceInfo | null) => void;
  onVersionInfoChange?: (versionInfo: VersionInfo | null) => void;
  onError?: (error: string) => void;
  showIPSetting?: boolean;
  onIPSettingClick?: () => void;
  children?: React.ReactNode;
}

export interface SonarDeviceInfoData {
  deviceInfo: DeviceInfo | null;
  versionInfo: VersionInfo | null;
  versionError: string | null;
  isLoading: boolean;
}

const SonarDeviceInfo: React.FC<SonarDeviceInfoProps> = ({
  sonarIP,
  onDeviceInfoChange,
  onVersionInfoChange,
  onError,
  showIPSetting = true,
  onIPSettingClick,
  children
}) => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [versionError, setVersionError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 使用 useRef 来稳定回调函数的引用，避免不必要的重新渲染
  const onVersionInfoChangeRef = useRef(onVersionInfoChange);
  const onDeviceInfoChangeRef = useRef(onDeviceInfoChange);
  const onErrorRef = useRef(onError);
  
  // 更新 ref 的值，但不触发重新渲染
  useEffect(() => {
    onVersionInfoChangeRef.current = onVersionInfoChange;
    onDeviceInfoChangeRef.current = onDeviceInfoChange;
    onErrorRef.current = onError;
  });

  // 获取版本信息
  const fetchVersion = useCallback(async () => {
    try {
      const response = await axios.get<VersionInfo>(`http://${sonarIP}/api/v1/version`);
      setVersionInfo(response.data);
      setVersionError(null);
      onVersionInfoChangeRef.current?.(response.data);
    } catch {
      const error = '版本获取失败';
      setVersionError(error);
      setVersionInfo(null);
      onVersionInfoChangeRef.current?.(null);
      onErrorRef.current?.(error);
    }
  }, [sonarIP]);

  // 同步设备时间
  const syncDeviceTime = useCallback(async (currentBrowserTime: number) => {
    try {
      await axios.put(`http://${sonarIP}/api/v1/time`, {
        timestamp: currentBrowserTime
      });
      console.log('设备时间同步成功');
    } catch (error) {
      console.error('设备时间同步失败:', error);
    }
  }, [sonarIP]);

  // 检查并同步时间
  const checkAndSyncTime = useCallback((deviceTimestamp: number) => {
    const browserTime = Date.now();
    const timeDiff = Math.abs(browserTime - deviceTimestamp);
    
    if (timeDiff > 60000) { // 超过1分钟
      console.log(`设备时间差距过大: ${timeDiff}ms，开始同步时间`);
      syncDeviceTime(browserTime);
    }
  }, [syncDeviceTime]);

  // 获取设备信息
  const fetchDeviceInfo = useCallback(async () => {
    try {
      const response = await axios.get<DeviceInfo>(`http://${sonarIP}/api/v1/hello`);
      setDeviceInfo(response.data);
      onDeviceInfoChangeRef.current?.(response.data);
      
      // 检查并同步设备时间
      if (response.data.unixTimestamp) {
        checkAndSyncTime(response.data.unixTimestamp);
      }
    } catch {
      const error = '设备信息获取失败';
      setDeviceInfo(null);
      onDeviceInfoChangeRef.current?.(null);
      onErrorRef.current?.(error);
    }
  }, [sonarIP, checkAndSyncTime]);

  // 下载日志
  const downloadLogs = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`http://${sonarIP}/log`, {
        responseType: 'text'
      });
      
      const logContent = response.data;
      const now = new Date();
      const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const deviceIdSuffix = deviceInfo?.deviceId ? `-${deviceInfo.deviceId}` : '';
      const filename = `sonar-log-${timestamp}${deviceIdSuffix}.txt`;
      
      const blob = new Blob([logContent], { type: 'text/plain;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      setIsDropdownOpen(false);
    } catch (error) {
      console.error('下载日志失败:', error);
      onErrorRef.current?.('下载日志失败，请检查设备连接');
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化数据获取 - 只在 sonarIP 变化时重新获取
  useEffect(() => {
    fetchVersion();
    fetchDeviceInfo();
  }, [fetchVersion, fetchDeviceInfo]);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 渲染版本信息
  const renderVersionInfo = () => {
    if (versionError) {
      return (
        <div className="text-red-400 text-xs">
          <div className="font-medium">版本信息</div>
          <div>{versionError}</div>
        </div>
      );
    }
    
    if (versionInfo) {
      return (
        <div className="text-gray-300 text-xs space-y-1">
          <div className="font-medium text-white">版本信息</div>
          <div>算法版本: {versionInfo.FPGA}</div>
          <div>系统版本: {versionInfo.Embedded}</div>
        </div>
      );
    }
    
    return (
      <div className="text-gray-400 text-xs">
        <div className="font-medium">版本信息</div>
        <div>加载中...</div>
      </div>
    );
  };

  // 渲染设备信息
  const renderDeviceInfo = () => (
    <div className="text-gray-300 text-xs space-y-1">
      <div className="font-medium text-white">设备信息</div>
      <div 
        className={showIPSetting ? "cursor-pointer" : ""}
        onClick={showIPSetting ? onIPSettingClick : undefined}
      >
        设备IP: <span className={`${showIPSetting ? 'text-blue-400 hover:text-blue-300 transition-colors' : 'text-gray-300'}`}>{sonarIP}</span>
      </div>
      {deviceInfo && (
        <>
          <div>设备编号: {deviceInfo.deviceId}</div>
          <div>声呐型号: {deviceInfo.platform}</div>
        </>
      )}
    </div>
  );

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="text-gray-400 hover:text-white transition-colors p-1 flex items-center justify-center"
        title="查看技术信息"
        disabled={isLoading}
      >
        <Info size={20} strokeWidth={2.5} />
      </button>
      
      {isDropdownOpen && (
        <div className="absolute right-0 top-full mt-2 w-64 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-50">
          <div className="p-3 space-y-3">
            {renderDeviceInfo()}
            
            <div className="border-t border-gray-600 pt-3">
              {renderVersionInfo()}
            </div>
            
            <div className="border-t border-gray-600 pt-3">
              <button
                onClick={downloadLogs}
                disabled={isLoading}
                className="flex items-center space-x-2 w-full text-left text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download size={16} />
                <span className="text-xs">{isLoading ? '下载中...' : '下载日志'}</span>
              </button>
            </div>
            
            {children && (
              <div className="border-t border-gray-600 pt-3">
                {children}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SonarDeviceInfo;