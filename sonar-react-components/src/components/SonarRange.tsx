import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';

// TypeScript interfaces and types
interface DefaultConfig {
  // 量程相关配置
  defaultModeIndex: number;
  minModeIndex: number;
  maxModeIndex: number;
  minRangeValue: number;
  
  // 视图相关配置
  defaultZoomScale: number;
  defaultZoomCenter: number;
  visibleAreaMin: number;
  visibleAreaMax: number;
  outOfViewLeft: number;
  outOfViewRight: number;
  rightLabelThreshold: number;
  
  // 时间相关配置
  statusPollingInterval: number;
  modeUpdateDelay: number;
  
  // 量程与模式配置
  rangeMeterFromMode: number[];
  pingPeriodFromMode: number[];
  keyModeMarkers: number[];
  
  // API配置
  apiEndpoint: string;
}

interface SonarRangeProps {
  sonarIP: string;
  config?: Partial<DefaultConfig>;
}

interface TooltipProps {
  show: boolean;
  value: TooltipValue | null;
  position: Position;
  pinging: string;
}

interface TooltipValue {
  range: number;
  mode: number;
}

interface Position {
  x: number;
  y: number;
}

interface ZoomViewProps {
  cfg: DefaultConfig;
  showZoomControl: boolean;
  zoomCenterPosition: number;
  currentMode: number;
  pinging: string;
  availableModes: number[];
  calculateZoomRange: (centerPosition: number) => ZoomRange;
  handleZoomClick: (e: React.MouseEvent) => void;
  handleZoomMouseMove: (e: React.MouseEvent) => void;
  setShowZoomControl: (show: boolean) => void;
  getRangeMeterByMode: (modeIndex: number) => number;
  tooltipValue: TooltipValue | null;
}

interface ZoomRange {
  zoomRangeMin: number;
  zoomRangeMax: number;
  zoomRangeSpan: number;
}

interface RangeInfo {
  minRange: number;
  maxRange: number;
  rangeSpan: number;
}

interface MainRangeBarProps {
  rangeBarRef: React.RefObject<HTMLDivElement | null>;
  currentMode: number;
  pinging: string;
  getPositionPercentage: (modeIndex: number) => number;
  handleRangeBarClick: (e: React.MouseEvent) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  showTooltip: boolean;
  setShowTooltip: (show: boolean) => void;
  setShowZoomControl: (show: boolean) => void;
  setZoomCenterPosition: (position: number) => void;
  getRangeInfo: () => RangeInfo;
  getRangeMeterByMode: (modeIndex: number) => number;
  maxModeIndex: number;
  rightLabelThreshold: number;
  showZoomControl: boolean;
}

interface ErrorInfo {
  type: 'network' | 'general';
  title: string;
  message: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  buttonColor: string;
  iconClass: string;
  icon: React.ReactNode;
  animation: string;
}

// 默认配置对象 - 提取所有硬编码的配置项
const DEFAULT_CONFIG: DefaultConfig = {
  // 量程相关配置
  defaultModeIndex: 7,  // 默认模式索引(1米)
  minModeIndex: 0,      // 最小模式索引
  maxModeIndex: 26,     // 最大模式索引(80米)
  minRangeValue: 0,     // 最小量程值(米)
  
  // 视图相关配置
  defaultZoomScale: 4,      // 默认放大倍数
  defaultZoomCenter: 0.5,   // 默认放大中心位置
  visibleAreaMin: 0,        // 可见区域最小百分比
  visibleAreaMax: 100,      // 可见区域最大百分比
  outOfViewLeft: -10,       // 超出左侧视图
  outOfViewRight: 110,      // 超出右侧视图
  rightLabelThreshold: 90,  // 右侧标签显示阈值
  
  // 时间相关配置
  statusPollingInterval: 1000,  // 状态轮询间隔(毫秒)
  modeUpdateDelay: 300,         // 模式更新后的延迟(毫秒)
  
  // 量程与模式配置
  // 模式对应的量程值(米)
  rangeMeterFromMode: [
    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 1.0, 2.0, 3.0,
    4.0, 5.0, 6.0, 8.0, 10.0, 12.0, 14.0, 16.0, 20.0, 25.0,
    30.0, 35.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0, 120.0,
    140.0, 160.0, 180.0, 200.0
  ],
  
  // 每个模式对应的pingPeriod值(毫秒)
  pingPeriodFromMode: [
    1000.0 / 40, 1000.0 / 40, 1000.0 / 40, 1000.0 / 40, 1000.0 / 40, 
    1000.0 / 40, 1000.0 / 40, 1000.0 / 40, 1000.0 / 30, 1000.0 / 20,
    1000.0 / 18, 1000.0 / 15, 1000.0 / 15, 1000.0 / 15, 1000.0 / 15, 
    1000.0 / 15, 1000.0 / 15, 1000.0 / 12, 1000.0 / 12, 1000.0 / 12,
    1000.0 / 12, 1000.0 / 12, 1000.0 / 10, 1000.0 / 10, 1000.0 / 9, 
    1000.0 / 8,  1000.0 / 7,  1000.0 / 6,  1000.0 / 5,  1000.0 / 5,
    1000.0 / 4,  1000.0 / 4,  1000.0 / 4,  1000.0 / 3
  ],
  
  // 显示的关键标记点(需在可用模式范围内)
  keyModeMarkers: [7, 14, 18, 22, 26],
  
  // API配置
  apiEndpoint: '/api/v1/sonar'
};

// 合并用户配置与默认配置
const mergeConfig = (defaultConfig: DefaultConfig, userConfig: Partial<DefaultConfig> = {}): DefaultConfig => {
  return { ...defaultConfig, ...userConfig };
};

// 工具提示组件
const Tooltip: React.FC<TooltipProps> = ({ show, value, position, pinging }) => {
  if (!show || !value) return null;
  
  return (
    <div 
      className={`absolute text-white text-xs px-2 py-1 rounded pointer-events-none shadow-lg transform -translate-x-1/2 ${
        pinging === 'on' ? 'bg-blue-700' : 'bg-gray-700'
      }`}
      style={{ 
        left: position.x,
        bottom: '0'
      }}
    >
      {value.range}m
    </div>
  );
};

// 放大视图组件
const ZoomView: React.FC<ZoomViewProps> = ({ 
  cfg, 
  showZoomControl, 
  zoomCenterPosition, 
  currentMode, 
  pinging, 
  availableModes, 
  calculateZoomRange,
  handleZoomClick,
  handleZoomMouseMove,
  setShowZoomControl,
  getRangeMeterByMode,
  tooltipValue
}) => {
  const {
    visibleAreaMin: VISIBLE_AREA_MIN,
    visibleAreaMax: VISIBLE_AREA_MAX,
    outOfViewLeft: OUT_OF_VIEW_LEFT,
    outOfViewRight: OUT_OF_VIEW_RIGHT,
    keyModeMarkers
  } = cfg;
  
  // 计算放大视图中某个模式的位置百分比
  const getZoomPositionPercentage = (modeIndex: number): number => {
    // 计算模式在放大视图中的位置
    const { zoomRangeMin, zoomRangeMax, zoomRangeSpan } = calculateZoomRange(zoomCenterPosition);
    const modeRange = getRangeMeterByMode(modeIndex);
    
    if (modeRange < zoomRangeMin) return OUT_OF_VIEW_LEFT; // 在可视区域外（左侧）
    if (modeRange > zoomRangeMax) return OUT_OF_VIEW_RIGHT; // 在可视区域外（右侧）
    
    return ((modeRange - zoomRangeMin) / zoomRangeSpan) * 100;
  };
  
  return (
    <div className={`w-full relative mb-0.5 transition-all duration-300 ease-in-out overflow-hidden ${
      showZoomControl ? 'h-3 opacity-100' : 'h-0 opacity-0'
    }`}>
      <div
        className={`zoom-control absolute inset-0 cursor-pointer transition-all duration-200 ${
          showZoomControl ? (pinging === 'on'
            ? 'bg-gradient-to-r from-blue-100 to-blue-300 shadow-md'
            : 'bg-gradient-to-r from-gray-200 to-gray-300 shadow')
          : 'bg-transparent'
        }`}
        onClick={showZoomControl ? handleZoomClick : undefined}
        onMouseMove={showZoomControl ? handleZoomMouseMove : undefined}
        onMouseEnter={() => setShowZoomControl(true)}
        onMouseLeave={() => {
          // 不立即隐藏，而是让父容器的mouseLeave处理
        }}
      >
        {showZoomControl && (
          <>
            {/* 放大视图中的模式标记 */}
            {availableModes.map(modeIndex => {
              const position = getZoomPositionPercentage(modeIndex);
              // 仅显示在可视区域内的刻度
              if (position >= VISIBLE_AREA_MIN && position <= VISIBLE_AREA_MAX) {
                return (
                  <div
                    key={modeIndex}
                    className={`absolute top-0 w-0.5 transform -translate-x-1/2 ${
                      keyModeMarkers.includes(modeIndex) ? 'h-full' : 'h-2/3 top-1/3'
                    } ${
                      (tooltipValue && tooltipValue.mode === modeIndex)
                        ? 'bg-yellow-600 w-1'
                        : modeIndex === currentMode
                          ? 'bg-blue-600 w-1'
                          : pinging === 'on'
                            ? 'bg-blue-400'
                            : 'bg-gray-500'
                    }`}
                    style={{ left: `${position}%` }}
                  />
                );
              }
              return null;
            })}
            
            {/* 放大视图中的当前位置指示器 */}
            <div
              className={`absolute top-0 w-1 h-full transform -translate-x-1/2 ${
                pinging === 'on' ? 'bg-blue-600' : 'bg-gray-600'
              }`}
              style={{
                left: `${getZoomPositionPercentage(currentMode)}%`,
                display: getZoomPositionPercentage(currentMode) >= VISIBLE_AREA_MIN && 
                        getZoomPositionPercentage(currentMode) <= VISIBLE_AREA_MAX ? 'block' : 'none'
              }}
            />
            
            {/* 放大视图中的量程值标签 - 统一单位为 m */}
            {keyModeMarkers.map(modeIndex => {
              const position = getZoomPositionPercentage(modeIndex);
              if (position >= VISIBLE_AREA_MIN && position <= VISIBLE_AREA_MAX) {
                return (
                  <div
                    key={`label-${modeIndex}`}
                    className="absolute bottom-full mb-0.5 text-xs transform -translate-x-1/2"
                    style={{ left: `${position}%` }}
                  >
                    {getRangeMeterByMode(modeIndex)}m
                  </div>
                );
              }
              return null;
            })}
          </>
        )}
      </div>
    </div>
  );
};

// 主进度条组件
const MainRangeBar: React.FC<MainRangeBarProps> = ({ 
  rangeBarRef, 
  currentMode, 
  pinging, 
  getPositionPercentage, 
  handleRangeBarClick, 
  handleMouseMove, 
  showTooltip, 
  setShowTooltip, 
  setShowZoomControl, 
  setZoomCenterPosition, 
  getRangeInfo, 
  getRangeMeterByMode, 
  maxModeIndex,
  rightLabelThreshold,
  showZoomControl
}) => {
  return (
    <>
      {/* 进度条容器 - 添加动态高度和过渡效果 */}
      <div 
        ref={rangeBarRef}
        className={`relative w-full cursor-pointer rounded-full transition-all duration-300 ease-in-out ${
          pinging === 'on'
            ? 'bg-gradient-to-r from-blue-100 to-blue-300'
            : 'bg-gradient-to-r from-gray-200 to-gray-300'
        } ${
          showZoomControl ? 'h-3' : 'h-5'
        }`}
        onClick={handleRangeBarClick}
        onMouseMove={handleMouseMove}
        onMouseEnter={() => {
          setShowTooltip(true);
          // 仅在初次移入时激活放大视图
          if (!showTooltip) {
            setShowZoomControl(true);
            // 初始位置设为当前模式的相对位置
            const { minRange, rangeSpan } = getRangeInfo();
            const currentRange = getRangeMeterByMode(currentMode);
            const initialPosition = (currentRange - minRange) / rangeSpan;
            setZoomCenterPosition(initialPosition);
          }
        }}
        onMouseLeave={(e) => {
          // 如果鼠标正在向放大视图区域移动，不要隐藏tooltip
          const relatedTarget = e.relatedTarget as HTMLElement | null;
          const movingToZoom = relatedTarget && 
            (relatedTarget.closest('.zoom-control') || 
             relatedTarget.classList.contains('zoom-control'));
          
          if (!movingToZoom) {
            setShowTooltip(false);
          }
        }}
      >
        {/* 声呐运行状态指示器 - 移到控制条最左侧 */}
        <div className="absolute -left-4 top-1/2 transform -translate-y-1/2">
          <div className={`w-2 h-2 rounded-full ${pinging === 'on' ? 'bg-blue-600 animate-ping' : 'bg-gray-400'}`}></div>
        </div>
        
        {/* 高亮当前量程部分 */}
        <div 
          className={`absolute top-0 left-0 h-full transition-all duration-300 ease-in-out rounded-full ${
            pinging === 'on' 
              ? 'bg-gradient-to-r to-blue-600 from-blue-400 shadow-md' 
              : 'bg-gray-500'
          }`}
          style={{ width: `${getPositionPercentage(currentMode)}%` }}
        >
          {/* 声呐工作状态指示器 - 使用Tailwind动画 */}
          {pinging === 'on' && (
            <div className="absolute inset-0 flex items-center">
              <div className="h-full aspect-square rounded-full ml-auto mr-0 bg-blue-500 opacity-40 shadow-[0_0_10px_rgba(59,130,246,0.8)]"></div>
            </div>
          )}
        </div>
        
        {/* 在活动状态下添加波纹效果 */}
        {pinging === 'on' && (
          <div 
            className="absolute top-0 left-0 h-full w-full overflow-hidden pointer-events-none"
            style={{ width: `${getPositionPercentage(currentMode)}%` }}
          >
            <div className="absolute inset-0 flex justify-end">
              <div className="w-full h-full bg-gradient-to-r from-transparent via-blue-300 to-transparent opacity-20 animate-pulse duration-600"></div>
            </div>
          </div>
        )}
      </div>
      
      {/* 量程值显示 - 当前量程显示在对应位置下方 */}
      <div className="relative w-full mt-1 h-5">
        {/* 80m量程值 - 当当前量程值太靠近右侧时隐藏 */}
        {getPositionPercentage(currentMode) < rightLabelThreshold && (
          <span className="absolute right-0 text-xs text-gray-500">{getRangeMeterByMode(maxModeIndex)}m</span>
        )}
        
        {/* 当前量程值显示在滑块位置下方 */}
        <div 
          className={`absolute text-xs font-medium ${pinging === 'on' ? 'text-blue-600' : 'text-gray-600'}`} 
          style={{ 
            left: `${getPositionPercentage(currentMode)}%`, 
            transform: 'translateX(-50%)' 
          }}
        >
          {getRangeMeterByMode(currentMode)}m
        </div>
      </div>
    </>
  );
};

// 主组件
const SonarRange: React.FC<SonarRangeProps> = ({ 
  sonarIP,
  config = {} 
}) => {
  // 合并配置
  const cfg = mergeConfig(DEFAULT_CONFIG, config);
  
  // 提取配置常量
  const {
    defaultModeIndex: DEFAULT_MODE_INDEX,
    maxModeIndex: MAX_MODE_INDEX,
    minRangeValue: MIN_RANGE_VALUE,
    defaultZoomScale: DEFAULT_ZOOM_SCALE,
    defaultZoomCenter: DEFAULT_ZOOM_CENTER,
    rightLabelThreshold: RIGHT_LABEL_THRESHOLD,
    statusPollingInterval: STATUS_POLLING_INTERVAL,
    modeUpdateDelay: MODE_UPDATE_DELAY,
    rangeMeterFromMode,
    pingPeriodFromMode,
    apiEndpoint: API_ENDPOINT
  } = cfg;
  
  // 计算可用模式范围 - 基于配置动态生成
  const availableModes = Array.from(
    { length: MAX_MODE_INDEX - DEFAULT_MODE_INDEX + 1 }, 
    (_, i) => i + DEFAULT_MODE_INDEX
  );
  
  // 状态定义
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentMode, setCurrentMode] = useState<number>(DEFAULT_MODE_INDEX); // 默认为1米量程
  const [showTooltip, setShowTooltip] = useState<boolean>(false);
  const [tooltipPosition, setTooltipPosition] = useState<Position>({ x: 0, y: 0 });
  const [tooltipValue, setTooltipValue] = useState<TooltipValue | null>(null);
  const [pinging, setPinging] = useState<string>('off'); // 添加pinging状态
  const rangeBarRef = useRef<HTMLDivElement>(null);
  const [showZoomControl, setShowZoomControl] = useState<boolean>(false);
  const [zoomCenterPosition, setZoomCenterPosition] = useState<number>(DEFAULT_ZOOM_CENTER);
  const [zoomScale] = useState<number>(DEFAULT_ZOOM_SCALE); // 放大倍数
  const [connectionFailed, setConnectionFailed] = useState<boolean>(false);
  const [retryInterval, setRetryInterval] = useState<number>(STATUS_POLLING_INTERVAL);

  const baseUrl = `http://${sonarIP}`;

  // 工具函数: 获取模式对应的量程值
  const getRangeMeterByMode = (modeIndex: number): number => {
    return rangeMeterFromMode[modeIndex];
  };

  // 辅助函数：获取量程范围信息（重复使用的计算）
  const getRangeInfo = (): RangeInfo => {
    const minRange = MIN_RANGE_VALUE;
    const maxRange = rangeMeterFromMode[MAX_MODE_INDEX];
    const rangeSpan = maxRange - minRange;
    return { minRange, maxRange, rangeSpan };
  };
  
  // 辅助函数：根据给定量程值找到最接近的模式
  const findClosestMode = (targetRange: number): number => {
    let closestMode = DEFAULT_MODE_INDEX;
    let minDiff = Infinity;
    
    availableModes.forEach(modeIndex => {
      const modeDiff = Math.abs(rangeMeterFromMode[modeIndex] - targetRange);
      if (modeDiff < minDiff) {
        minDiff = modeDiff;
        closestMode = modeIndex;
      }
    });
    
    return closestMode;
  };

  // 辅助函数：计算缩放视图的范围信息
  const calculateZoomRange = (centerPosition: number): ZoomRange => {
    const { minRange, maxRange, rangeSpan } = getRangeInfo();
    const zoomWidth = 1.0 / zoomScale;
    
    let zoomRangeMin: number, zoomRangeMax: number;
    
    if (centerPosition < zoomWidth / 2) {
      // 靠近左侧边界
      zoomRangeMin = minRange;
      zoomRangeMax = minRange + zoomWidth * rangeSpan;
    } else if (centerPosition > 1 - zoomWidth / 2) {
      // 靠近右侧边界
      zoomRangeMin = maxRange - zoomWidth * rangeSpan;
      zoomRangeMax = maxRange;
    } else {
      // 中间区域
      zoomRangeMin = minRange + (centerPosition - zoomWidth / 2) * rangeSpan;
      zoomRangeMax = minRange + (centerPosition + zoomWidth / 2) * rangeSpan;
    }
    
    const zoomRangeSpan = zoomRangeMax - zoomRangeMin;
    
    return { zoomRangeMin, zoomRangeMax, zoomRangeSpan };
  };

  // 获取声呐状态
  const fetchSonarStatus = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await axios.get(`${baseUrl}${API_ENDPOINT}`, {
        timeout: 2000, 
      });
      // 获取当前工作模式
      let mode = response.data.mode;
      
      // 确保模式在可用范围内
      if (mode < DEFAULT_MODE_INDEX || mode > MAX_MODE_INDEX) {
        mode = DEFAULT_MODE_INDEX; // 默认设为1米
      }
      
      // 获取并设置pinging状态
      const pingStatus = response.data.pinging || 'off';
      setPinging(pingStatus);
      
      setCurrentMode(mode);
      setError(null);
      // 连接恢复，重置轮询间隔
      if (connectionFailed) {
        setConnectionFailed(false);
        setRetryInterval(STATUS_POLLING_INTERVAL);
      }
    } catch (err: unknown) {
      // 错误处理：区分网络错误和其他错误
      const error = err as { code?: string; response?: unknown; message?: string };
      if (error.code === 'ECONNABORTED' || !error.response) {
        setError('网络连接失败：请检查设备连接');
        setPinging('off'); // 网络错误时自动将ping状态设为off
        setConnectionFailed(true);
        // 连接失败时使用更短的重试间隔
        setRetryInterval(300); // 300毫秒的快速重试
      } else {
        setError('获取声呐状态失败：' + error.message);
        setConnectionFailed(false); // 非网络错误
        setRetryInterval(STATUS_POLLING_INTERVAL); // 使用标准轮询间隔
      }
    } finally {
      setIsLoading(false);
    }
  }, [baseUrl, API_ENDPOINT, DEFAULT_MODE_INDEX, MAX_MODE_INDEX, connectionFailed, STATUS_POLLING_INTERVAL]);

  // 组件加载时获取初始状态
  useEffect(() => {
    fetchSonarStatus();
    // 使用动态的轮询间隔
    const interval = setInterval(fetchSonarStatus, retryInterval);
    return () => clearInterval(interval);
  }, [sonarIP, retryInterval, fetchSonarStatus]); // 添加retryInterval和fetchSonarStatus作为依赖项

  // 更新声呐工作模式
  const updateSonarMode = async (newMode: number): Promise<void> => {
    if (newMode === currentMode) return; // 避免不必要的更新
    
    setIsLoading(true);
    try {
      const response = await axios.put(
        `${baseUrl}${API_ENDPOINT}`,
        { 
          mode: newMode,
          pingPeriod: pingPeriodFromMode[newMode]
        },
        { 
          headers: { 'Content-Type': 'application/json' },
          timeout: 2000 // 添加超时设置
        }
      );
      
      if (response.data.status === 'success') {
        setCurrentMode(newMode);
        
        // 添加短暂延时，确保服务器有足够时间应用设置
        setTimeout(async () => {
          await fetchSonarStatus(); // 更新后重新获取状态
        }, MODE_UPDATE_DELAY);
      } else {
        setError('工作模式更新失败：' + response.data.message);
        setConnectionFailed(false); // 非网络错误
      }
    } catch (err: unknown) {
      // 错误处理：区分网络错误和其他错误
      const error = err as { code?: string; response?: unknown; message?: string };
      if (error.code === 'ECONNABORTED' || !error.response) {
        setError('网络连接失败：请检查设备连接');
        setPinging('off'); // 网络错误时自动将ping状态设为off
        setConnectionFailed(true);
        // 连接失败时使用更短的重试间隔
        setRetryInterval(300); // 300毫秒的快速重试
      } else {
        setError('工作模式更新失败：' + error.message);
        setConnectionFailed(false); // 非网络错误
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 辅助函数：更新tooltip数据
  const updateTooltip = (closestMode: number, clientX: number): void => {
    setTooltipValue({
      range: rangeMeterFromMode[closestMode],
      mode: closestMode
    });
    
    // 设置tooltip位置，使用相对位置
    if (rangeBarRef.current) {
      const rect = rangeBarRef.current.getBoundingClientRect();
      setTooltipPosition({
        x: clientX - rect.left, // 使用相对于容器的位置
        y: 0 // y位置不再需要，因为使用了固定的bottom定位
      });
    }
    
    // 确保提示框显示
    setShowTooltip(true);
  };

  // 计算当前模式的量程标记位置（百分比）
  const getPositionPercentage = (modeIndex: number): number => {
    const { minRange, rangeSpan } = getRangeInfo();
    const modeRange = rangeMeterFromMode[modeIndex];
    return ((modeRange - minRange) / rangeSpan) * 100;
  };

  // 处理进度条点击
  const handleRangeBarClick = (e: React.MouseEvent): void => {
    if (isLoading) return;
    
    const bar = rangeBarRef.current;
    if (!bar) return;
    
    const rect = bar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    
    // 根据点击位置计算最接近的可用模式
    const { minRange, rangeSpan } = getRangeInfo();
    const clickedRange = minRange + (clickPosition * rangeSpan);
    
    // 找到最接近点击位置的可用模式
    const closestMode = findClosestMode(clickedRange);
    
    updateSonarMode(closestMode);
  };

  // 处理鼠标悬停在进度条上
  const handleMouseMove = (e: React.MouseEvent): void => {
    if (!rangeBarRef.current) return;
    
    const bar = rangeBarRef.current;
    const rect = bar.getBoundingClientRect();
    const hoverPosition = (e.clientX - rect.left) / rect.width;
    
    // 显示放大视图，并初始化中心位置
    if (!showZoomControl) {
      setShowZoomControl(true);
      setZoomCenterPosition(hoverPosition);
    } else {
      // 更新放大视图的中心位置
      setZoomCenterPosition(hoverPosition);
    }
    
    // 计算悬停位置对应的量程
    const { minRange, rangeSpan } = getRangeInfo();
    const hoverRange = minRange + (hoverPosition * rangeSpan);
    
    // 找到最接近悬停位置的可用模式
    const closestMode = findClosestMode(hoverRange);
    
    updateTooltip(closestMode, e.clientX);
  };

  
  // 新增：处理整个控件区域的鼠标移出事件
  const handleContainerMouseLeave = (): void => {
    setShowTooltip(false);
    // 添加延迟隐藏ZoomView，为动画效果提供缓冲时间
    setTimeout(() => {
      setShowZoomControl(false);
    }, 300);
  };

  // 处理鼠标在放大视图上的移动
  const handleZoomMouseMove = (e: React.MouseEvent): void => {
    if (!rangeBarRef.current) return;
    
    // 确保放大视图保持显示
    if (!showZoomControl) {
      setShowZoomControl(true);
    }
    
    const bar = rangeBarRef.current;
    const rect = bar.getBoundingClientRect();
    const mousePosition = (e.clientX - rect.left) / rect.width;
    
    // 更新中心位置
    setZoomCenterPosition(mousePosition);
    
    // 计算放大视图的量程范围
    const { zoomRangeMin, zoomRangeSpan } = calculateZoomRange(mousePosition);
    
    // 计算悬停位置对应的量程值
    const hoverRange = zoomRangeMin + (mousePosition * zoomRangeSpan);
    
    // 找到最接近悬停位置的可用模式
    const closestMode = findClosestMode(hoverRange);
    
    updateTooltip(closestMode, e.clientX);
  };

  // 处理放大视图中的点击
  const handleZoomClick = (e: React.MouseEvent): void => {
    if (isLoading) return;
    
    const bar = rangeBarRef.current;
    if (!bar) return;
    
    const rect = bar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    
    // 计算放大视图的量程范围
    const { zoomRangeMin, zoomRangeSpan } = calculateZoomRange(zoomCenterPosition);
    
    // 计算点击位置对应的量程值
    const clickedRange = zoomRangeMin + (clickPosition * zoomRangeSpan);
    
    // 找到最接近点击位置的可用模式
    const closestMode = findClosestMode(clickedRange);
    
    updateSonarMode(closestMode);
  };

  // 工具函数: 获取错误显示的类型和内容
  const getErrorInfo = (): ErrorInfo | null => {
    if (!error) return null;
    
    // 网络连接错误
    if (connectionFailed) {
      return {
        type: 'network',
        title: '网络连接失败',
        message: '网络连接已断开，正在尝试重新连接...',
        bgColor: 'bg-red-50',
        textColor: 'text-red-600',
        borderColor: 'border-red-200',
        buttonColor: 'bg-red-600 hover:bg-red-700',
        iconClass: 'text-red-500',
        icon: (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        ),
        animation: 'animate-pulse'
      };
    }
    
    // 其他类型错误
    return {
      type: 'general',
      title: '操作失败',
      message: error,
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
      borderColor: 'border-orange-200',
      buttonColor: 'bg-orange-600 hover:bg-orange-700',
      iconClass: 'text-orange-500',
      icon: (
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      ),
      animation: ''
    };
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="p-2">
      {/* 状态指示器和量程组件容器 */}
      <div className="relative min-h-[84px]">
        {/* 错误状态指示器 - 在有错误时显示并覆盖量程组件 */}
        {error && errorInfo && (
          <div className={`rounded-md shadow-sm transition-all duration-300 ${errorInfo.bgColor} border ${errorInfo.borderColor}
                        absolute top-0 left-0 w-full h-full z-10 flex items-center justify-center overflow-hidden ${errorInfo.animation}`}>
            <div className="flex items-center px-3 py-2 w-full max-w-[95%] md:max-w-[90%]">
              {/* 图标 */}
              <div className={`${errorInfo.iconClass} mr-2 flex-shrink-0`}>
                {errorInfo.icon}
              </div>
              
              {/* 文本和按钮区域 */}
              <div className="flex-grow min-w-0">
                {/* 错误标题 */}
                <div className="font-medium text-base flex items-center truncate">
                  {errorInfo.title}
                </div>
                
                {/* 错误信息 */}
                <p className="text-sm opacity-90 truncate">{errorInfo.message}</p>
              </div>
              
              {/* 按钮 */}
              <button 
                className={`ml-3 flex-shrink-0 px-3 py-1 text-sm text-white rounded transition-colors ${errorInfo.buttonColor} shadow-sm`}
                onClick={fetchSonarStatus}
              >
                立即重试
              </button>
            </div>
          </div>
        )}
      
        {/* 量程进度条 - 当有错误时将被状态指示器覆盖 */}
        <div 
          className={`relative py-2 transition-opacity duration-300 ${error ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
          onMouseLeave={handleContainerMouseLeave}
          aria-hidden={error ? true : false}
        >
          
          {/* 为tooltip预留固定空间 - 移到最上方确保固定高度 */}
          <div className="h-6 relative w-full flex items-center justify-center mb-0.5">
            {/* 悬停提示框 - 使用Tooltip组件 */}
            <Tooltip 
              show={showTooltip} 
              value={tooltipValue} 
              position={tooltipPosition} 
              pinging={pinging} 
            />
          </div>
          
          {/* 放大视图组件 */}
          <ZoomView 
            cfg={cfg}
            showZoomControl={showZoomControl}
            zoomCenterPosition={zoomCenterPosition}
            currentMode={currentMode}
            pinging={pinging}
            availableModes={availableModes}
            calculateZoomRange={calculateZoomRange}
            handleZoomClick={handleZoomClick}
            handleZoomMouseMove={handleZoomMouseMove}
            setShowZoomControl={setShowZoomControl}
            getRangeMeterByMode={getRangeMeterByMode}
            tooltipValue={tooltipValue}
          />
          
          {/* 主进度条组件 */}
          <MainRangeBar 
            rangeBarRef={rangeBarRef}
            currentMode={currentMode}
            pinging={pinging}
            getPositionPercentage={getPositionPercentage}
            handleRangeBarClick={handleRangeBarClick}
            handleMouseMove={handleMouseMove}
            showTooltip={showTooltip}
            setShowTooltip={setShowTooltip}
            setShowZoomControl={setShowZoomControl}
            setZoomCenterPosition={setZoomCenterPosition}
            getRangeInfo={getRangeInfo}
            getRangeMeterByMode={getRangeMeterByMode}
            maxModeIndex={MAX_MODE_INDEX}
            rightLabelThreshold={RIGHT_LABEL_THRESHOLD}
            showZoomControl={showZoomControl}
          />
        </div>
      </div>
    </div>
  );
};

export default SonarRange;