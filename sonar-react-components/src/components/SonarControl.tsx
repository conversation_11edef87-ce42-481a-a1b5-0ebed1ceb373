import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import axios from 'axios';

interface SonarConfig {
  beamNum: number;
  velocity: number;
  gain: number;
  gamma: number;
  pingPeriod: number;
}

interface SonarControlProps {
  sonarIP: string;
  advancedMode?: boolean;
}

interface VerifyResult {
  success: boolean;
  message: string;
}

const SonarControl: React.FC<SonarControlProps> = ({ sonarIP, advancedMode = false }) => {
  // 状态定义
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [verifyResult, setVerifyResult] = useState<VerifyResult | null>(null);
  const [isAdvancedMode, setIsAdvancedMode] = useState<boolean>(() => {
    const savedMode = localStorage.getItem('sonarControlAdvancedMode');
    return savedMode ? JSON.parse(savedMode) : advancedMode;
  });
  const [menuPosition, setMenuPosition] = useState<{top: number, left: number}>({top: 0, left: 0});
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuContentRef = useRef<HTMLDivElement>(null);
  
  // 默认配置
  const defaultConfig: SonarConfig = {
    beamNum: 256,
    velocity: 1500.0,
    gain: 50,
    gamma: 50,
    pingPeriod: 100
  };

  // 当前配置
  const [config, setConfig] = useState<SonarConfig>(defaultConfig);
  
  // 四舍五入 pingPeriod 到整数
  const roundPingPeriod = (value: number): number => {
    return Math.round(value);
  };
  
  const baseUrl = `http://${sonarIP}`;
  
  // 获取声呐状态
  const fetchSonarStatus = async (): Promise<SonarConfig | null> => {
    setIsLoading(true);
    try {
      const response = await axios.get(`${baseUrl}/api/v1/sonar`);
      const responseData: SonarConfig = {
        beamNum: response.data.beamNum,
        velocity: response.data.velocity,
        gain: response.data.gain,
        gamma: response.data.gamma,
        pingPeriod: roundPingPeriod(response.data.pingPeriod)
      };
      setConfig(responseData);
      setError(null);
      return responseData;
    } catch (err) {
      setError('获取声呐状态失败：' + (err instanceof Error ? err.message : String(err)));
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // 计算菜单位置 - 始终居中显示
  const calculateMenuPosition = (): void => {
    // 计算屏幕中心位置
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const menuWidth = 300;
    
    // 菜单居中定位
    const left = centerX - menuWidth / 2;
    const top = centerY - 200; // 稍微偏上一点，避免过于居中
    
    setMenuPosition({ top, left });
  };

  // 点击菜单按钮时获取声呐状态并展开菜单
  const handleMenuClick = async (): Promise<void> => {
    if (!isMenuOpen) {
      calculateMenuPosition();
      await fetchSonarStatus();
    }
    setIsMenuOpen(!isMenuOpen);
  };

  // 处理配置项变化
  const handleConfigChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>): void => {
    const { name, value, type } = e.target;
    const target = e.target as HTMLInputElement;
    let newValue: string | number | boolean;
    
    if (type === 'checkbox') {
      newValue = target.checked;
    } else if (type === 'number') {
      newValue = parseFloat(value);
    } else if (name === 'beamNum') {
      newValue = parseInt(value, 10);
    } else {
      newValue = value;
    }
    
    if (name === 'pingPeriod') {
      setConfig({ ...config, [name]: roundPingPeriod(newValue as number) });
    } else {
      setConfig({ ...config, [name]: newValue });
    }
  };

  // 提交配置更新
  const handleSubmit = async (): Promise<void> => {
    setIsLoading(true);
    setVerifyResult(null);
    try {
      const submittedConfig = {...config}; 
      
      submittedConfig.pingPeriod = roundPingPeriod(submittedConfig.pingPeriod);
      
      const response = await axios.put(
        `${baseUrl}/api/v1/sonar`,
        submittedConfig,
        { headers: { 'Content-Type': 'application/json' } }
      );
      
      if (response.data.status === 'success') {
        setIsSuccess(true);
        
        setIsVerifying(true);
        setTimeout(async () => {
          try {
            const latestConfig = await fetchSonarStatus();
            
            if (latestConfig) {
              const isMatch = Object.keys(submittedConfig).every(key => {
                const configKey = key as keyof SonarConfig;
                if (configKey === 'velocity') {
                  return Math.abs(submittedConfig[configKey] - latestConfig[configKey]) < 0.1;
                }
                return submittedConfig[configKey] === latestConfig[configKey];
              });
              
              if (isMatch) {
                setVerifyResult({ success: true, message: '配置已验证成功' });
              } else {
                const mismatchedParams = Object.keys(submittedConfig).filter(key => {
                  const configKey = key as keyof SonarConfig;
                  if (configKey === 'velocity') {
                    return Math.abs(submittedConfig[configKey] - latestConfig[configKey]) >= 0.1;
                  }
                  return submittedConfig[configKey] !== latestConfig[configKey];
                });
                
                setVerifyResult({ 
                  success: false, 
                  message: `以下参数未成功应用: ${mismatchedParams.join(', ')}` 
                });
              }
            }
          } catch {
            setVerifyResult({ success: false, message: '配置验证失败' });
          } finally {
            setIsVerifying(false);
          }
        }, 1000);
        
        setTimeout(() => {
          setIsSuccess(false);
        }, 1500);
      } else {
        setError('配置更新失败：' + response.data.message);
      }
    } catch (err) {
      setError('配置更新失败：' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };

  // 点击其他地方关闭菜单的处理函数
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent): void => {
      const target = event.target as Node;
      // 检查点击是否在按钮或菜单内
      if (menuRef.current && !menuRef.current.contains(target) && 
          menuContentRef.current && !menuContentRef.current.contains(target)) {
        setIsMenuOpen(false);
      }
    };

    const handleResize = (): void => {
      if (isMenuOpen) {
        calculateMenuPosition();
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('resize', handleResize);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('resize', handleResize);
    };
  }, [isMenuOpen]);

  // 处理模式切换
  const handleModeToggle = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const newMode = e.target.checked;
    setIsAdvancedMode(newMode);
    localStorage.setItem('sonarControlAdvancedMode', JSON.stringify(newMode));
  };

  return (
    <div className="relative inline-block" ref={menuRef}>
      <button 
        ref={buttonRef}
        className={`w-12 h-12 text-gray-400 hover:text-white transition-colors p-1 flex items-center justify-center focus:outline-none ${
          isLoading ? 'text-gray-500 cursor-not-allowed' : ''
        }`}
        onClick={handleMenuClick}
        disabled={isLoading}
      >
        {isLoading ? (
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        )}
      </button>
      
      {isMenuOpen && createPortal(
        <div 
          ref={menuContentRef}
          className="fixed z-[9999] min-w-[300px] bg-black/80 backdrop-blur-sm border border-gray-600 rounded shadow-lg p-4 max-h-[calc(100vh-16px)] overflow-y-auto"
          style={{
            top: `${menuPosition.top}px`,
            left: `${menuPosition.left}px`
          }}
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-bold text-white">声呐配置</h3>
            <div className="flex items-center text-sm">
              <label className="mr-2 cursor-pointer text-white">
                高级模式
                <input
                  type="checkbox"
                  className="ml-1 h-4 w-4"
                  checked={isAdvancedMode}
                  onChange={handleModeToggle}
                />
              </label>
            </div>
          </div>
          
          {error && <div className="text-red-400 mb-3">{error}</div>}
          {verifyResult && (
            <div className={`${verifyResult.success ? 'text-green-400' : 'text-orange-400'} mb-3 text-sm`}>
              {verifyResult.message}
            </div>
          )}
          
          {/* 增益 - 始终显示 */}
          <div className="flex items-center mb-3">
            <label className="w-30 font-semibold text-white">增益:</label>
            <input
              type="number"
              name="gain"
              min="0"
              max="255"
              value={config.gain}
              onChange={handleConfigChange}
              className="flex-1 p-2 border border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-gray-800 text-white"
            />
          </div>
          
          {/* Ping周期 - 始终显示 */}
          <div className="flex items-center mb-3">
            <label className="w-30 font-semibold text-white">Ping 周期 (ms):</label>
            <input
              type="number"
              name="pingPeriod"
              min="1"
              value={config.pingPeriod}
              onChange={handleConfigChange}
              className="flex-1 p-2 border border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-gray-800 text-white"
            />
          </div>
          
          {/* 高级选项 - 只在高级模式显示 */}
          {isAdvancedMode && (
            <>
              <div className="mt-4 mb-2 border-t border-gray-600 pt-2">
                <div className="text-sm text-gray-400 mb-2">高级选项</div>
              </div>
              
              <div className="flex items-center mb-3">
                <label className="w-30 font-semibold text-white">波束数量:</label>
                <select 
                  name="beamNum" 
                  value={config.beamNum} 
                  onChange={handleConfigChange}
                  className="flex-1 p-2 border border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-gray-800 text-white"
                >
                  <option value={256}>256</option>
                  <option value={512}>512</option>
                </select>
              </div>
              
              <div className="flex items-center mb-3">
                <label className="w-30 font-semibold text-white">声速 (m/s):</label>
                <input
                  type="number"
                  name="velocity"
                  min="1400"
                  max="1600"
                  step="0.1"
                  value={config.velocity}
                  onChange={handleConfigChange}
                  className="flex-1 p-2 border border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-gray-800 text-white"
                />
              </div>
              
              <div className="flex items-center mb-3">
                <label className="w-30 font-semibold text-white">伽马值:</label>
                <input
                  type="number"
                  name="gamma"
                  min="0"
                  max="255"
                  value={config.gamma}
                  onChange={handleConfigChange}
                  className="flex-1 p-2 border border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-gray-800 text-white"
                />
              </div>
              
            </>
          )}
          
          <div className="flex justify-between mt-4">
            <button 
              className={`px-4 py-2 rounded backdrop-blur-sm transition-all duration-200 ${
                isSuccess 
                  ? 'bg-blue-500/10 border border-blue-500/30 text-blue-300 hover:bg-blue-500/20 hover:border-blue-500/50 hover:shadow-lg' 
                  : isVerifying || isLoading
                    ? 'bg-gray-500/5 border border-gray-500/20 text-gray-500 cursor-not-allowed' 
                    : 'bg-blue-500/10 border border-blue-500/30 text-blue-400 hover:bg-blue-500/20 hover:border-blue-500/50 hover:shadow-lg'
              }`} 
              onClick={handleSubmit} 
              disabled={isVerifying || isLoading}
            >
              {isSuccess ? '更新成功！' : isVerifying ? '验证中...' : isLoading ? '更新中...' : '更改配置'}
            </button>
            <button 
              className="px-4 py-2 rounded backdrop-blur-sm bg-gray-500/5 border border-gray-500/40 text-gray-300 hover:bg-gray-500/10 hover:border-gray-500/60 hover:text-white hover:shadow-lg transition-all duration-200" 
              onClick={() => setIsMenuOpen(false)}
            >
              取消
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default SonarControl; 