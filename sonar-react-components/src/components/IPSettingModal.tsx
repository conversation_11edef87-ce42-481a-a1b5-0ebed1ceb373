import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import axios from 'axios';

export interface IPSettingModalProps {
  sonarIP: string;
  isOpen: boolean;
  onClose: () => void;
}

export interface IPInfo {
  ip: string;
  mask: string;
}

const IPSettingModal: React.FC<IPSettingModalProps> = ({ sonarIP, isOpen, onClose }) => {
  const [currentIP, setCurrentIP] = useState<IPInfo | null>(null);
  const [newIP, setNewIP] = useState('');
  const [newMask, setNewMask] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // IP地址格式验证
  const validateIP = (ip: string): boolean => {
    const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  };

  // 子网掩码格式验证
  const validateMask = (mask: string): boolean => {
    const maskRegex = /^(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)\.(255|254|252|248|240|224|192|128|0)$/;
    return maskRegex.test(mask);
  };

  // 获取当前IP信息
  const fetchCurrentIP = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get<IPInfo>(`http://${sonarIP}/api/v1/ip`);
      setCurrentIP(response.data);
      setNewIP(response.data.ip);
      setNewMask(response.data.mask);
    } catch {
      setError('获取当前IP信息失败');
      // 如果API失败，使用sonarIP作为默认值
      setNewIP(sonarIP);
      setNewMask('*************');
    } finally {
      setLoading(false);
    }
  }, [sonarIP]);

  // 设置新IP
  const handleSetIP = async () => {
    if (!validateIP(newIP)) {
      setError('IP地址格式不正确');
      return;
    }

    if (!validateMask(newMask)) {
      setError('子网掩码格式不正确');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await axios.put(`http://${sonarIP}/api/v1/ip`, {
        ip: newIP,
        mask: newMask
      });

      const result = response.data;
      
      if (result.status === 'success') {
        setSuccess('IP设置成功，即将重定向到新地址...');
        
        // 等待3秒后重定向
        setTimeout(() => {
          window.location.href = `http://${newIP}`;
        }, 3000);
      } else {
        setError(result.message || '设置失败');
      }
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'response' in error && 
          error.response && typeof error.response === 'object' && 
          'data' in error.response && error.response.data && 
          typeof error.response.data === 'object' && 'message' in error.response.data) {
        setError(error.response.data.message as string);
      } else {
        setError('设置IP失败，请检查网络连接');
      }
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (currentIP) {
      setNewIP(currentIP.ip);
      setNewMask(currentIP.mask);
    }
    setError(null);
    setSuccess(null);
  };

  // 关闭弹窗
  const handleClose = () => {
    setError(null);
    setSuccess(null);
    onClose();
  };

  // 当弹窗打开时获取当前IP
  useEffect(() => {
    if (isOpen) {
      fetchCurrentIP();
    }
  }, [isOpen, sonarIP, fetchCurrentIP]);

  if (!isOpen) return null;

  // Render modal using a portal to escape parent container constraints
  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-gray-800 rounded-lg p-6 w-96 max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white">IP地址设置</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white text-xl"
          >
            ×
          </button>
        </div>

        {loading && (
          <div className="text-center py-4">
            <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            <p className="text-gray-300 mt-2">加载中...</p>
          </div>
        )}

        {!loading && (
          <div className="space-y-4">
            {currentIP && (
              <div>
                <label className="text-sm text-gray-300">当前设置</label>
                <div className="bg-gray-900 p-3 rounded">
                  <p className="text-white">IP: {currentIP.ip}</p>
                  <p className="text-white">掩码: {currentIP.mask}</p>
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                新IP地址
              </label>
              <input
                type="text"
                value={newIP}
                onChange={(e) => setNewIP(e.target.value)}
                placeholder="*************"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                新子网掩码
              </label>
              <input
                type="text"
                value={newMask}
                onChange={(e) => setNewMask(e.target.value)}
                placeholder="*************"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {error && (
              <div className="bg-red-600 text-white p-3 rounded">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-600 text-white p-3 rounded">
                {success}
              </div>
            )}

            <div className="flex justify-between items-center">
              <button
                onClick={handleSetIP}
                disabled={loading}
                className="rounded backdrop-blur-sm bg-orange-500/15 border border-orange-500/50 text-orange-400 hover:bg-orange-500/25 hover:border-orange-500/70 hover:text-orange-300 hover:shadow-[0_0_15px_rgba(251,146,60,0.4)] transition-all duration-200 px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ⚠️ 设置
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={handleReset}
                  disabled={loading}
                  className="px-4 py-2 rounded backdrop-blur-sm bg-blue-500/5 border border-blue-500/25 text-blue-400 hover:bg-blue-500/10 hover:border-blue-500/40 hover:text-blue-300 hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  重置
                </button>
                <button
                  onClick={handleClose}
                  disabled={loading}
                  className="px-4 py-2 rounded backdrop-blur-sm bg-gray-500/5 border border-gray-500/40 text-gray-300 hover:bg-gray-500/10 hover:border-gray-500/60 hover:text-white hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default IPSettingModal;