import React, { useState, useEffect } from 'react';
import axios from 'axios';

const SonarControlTable = ({ sonarIP }) => {
  const defaultConfig = {
    beamNum: 256,
    velocity: 1500.0,
    gain: 50,
    gamma: 50,
    mode: 0,
    higherFreq: false,
    pingPeriod: 100
  };

  // 当前声呐状态，主要用于显示自动更新信息
  const [sonarStatus, setSonarStatus] = useState(null);
  // 用户的配置参数，用户修改时不会被自动更新覆盖
  const [config, setConfig] = useState(defaultConfig);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const baseUrl = `http://${sonarIP}`;

  // 定期获取声呐状态（仅用于显示，不覆盖用户正在修改的配置）
  const fetchSonarStatus = async () => {
    try {
      const response = await axios.get(`${baseUrl}/api/v1/sonar`);
      setSonarStatus(response.data);
      setConfig(prev => ({
        ...prev,
        beamNum: response.data.beamNum,
        velocity: response.data.velocity,
        gain: response.data.gain,
        gamma: response.data.gamma,
        mode: response.data.mode,
        higherFreq: response.data.higherFreq === "支持",
        pingPeriod: response.data.pingPeriod
      }));
      setError(null);
    } catch (err) {
      setError('获取声呐状态失败：' + err.message);
    }
  };

  useEffect(() => {
    fetchSonarStatus();
    const interval = setInterval(fetchSonarStatus, 1000); // 每5秒刷新当前状态
    return () => clearInterval(interval);
  }, [sonarIP]);

  // 当配置控件发生变化时，更新 config 并自动发送更新请求
  const handleConfigChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : type === 'number' ? parseFloat(value) : value;
    const updatedConfig = { ...config, [name]: newValue };
    setConfig(updatedConfig);
    updateConfig(updatedConfig);
  };

  // 更新配置至声呐
  const updateConfig = async (newConfig) => {
    setLoading(true);
    try {
      const response = await axios.put(
        `${baseUrl}/api/v1/sonar`,
        newConfig,
        { headers: { 'Content-Type': 'application/json' } }
      );
      if (response.data.status === 'success') {
        alert('配置更新成功');
        fetchSonarStatus();
      } else {
        setError('配置更新失败：' + response.data.message);
      }
    } catch (err) {
      setError('配置更新失败：' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // 重置配置到默认值
  const resetConfig = () => {
    setConfig(defaultConfig);
    updateConfig(defaultConfig);
  };

  return (
    <div className="sonar-control">
      <h2>声呐控制面板</h2>
      {error && <div className="error-message">{error}</div>}
      <table>
        <thead>
          <tr>
            <th>参数</th>
            <th>当前状态</th>
            <th>配置控制</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>波束数量</td>
            <td>{sonarStatus ? sonarStatus.beamNum : '加载中'}</td>
            <td>
              <select name="beamNum" value={config.beamNum} onChange={handleConfigChange}>
                <option value={256}>256</option>
                <option value={512}>512</option>
              </select>
            </td>
          </tr>
          <tr>
            <td>声速 (m/s)</td>
            <td>{sonarStatus ? sonarStatus.velocity : '加载中'}</td>
            <td>
              <input
                type="number"
                name="velocity"
                min="1400"
                max="1600"
                step="0.1"
                value={config.velocity}
                onChange={handleConfigChange}
              />
            </td>
          </tr>
          <tr>
            <td>增益</td>
            <td>{sonarStatus ? sonarStatus.gain : '加载中'}</td>
            <td>
              <input
                type="number"
                name="gain"
                min="0"
                max="255"
                value={config.gain}
                onChange={handleConfigChange}
              />
            </td>
          </tr>
          <tr>
            <td>伽马值</td>
            <td>{sonarStatus ? sonarStatus.gamma : '加载中'}</td>
            <td>
              <input
                type="number"
                name="gamma"
                min="0"
                max="255"
                value={config.gamma}
                onChange={handleConfigChange}
              />
            </td>
          </tr>
          <tr>
            <td>工作模式</td>
            <td>{sonarStatus ? sonarStatus.mode : '加载中'}</td>
            <td>
              <input
                type="number"
                name="mode"
                min="0"
                value={config.mode}
                onChange={handleConfigChange}
              />
            </td>
          </tr>
          <tr>
            <td>高频模式</td>
            <td>
              {sonarStatus
                ? (sonarStatus.higherFreq === "支持" ? "开启" : "关闭")
                : '加载中'}
            </td>
            <td>
              <input
                type="checkbox"
                name="higherFreq"
                checked={config.higherFreq}
                onChange={handleConfigChange}
              /> 高频模式
            </td>
          </tr>
          <tr>
            <td>Ping 周期 (ms)</td>
            <td>{sonarStatus ? sonarStatus.pingPeriod : '加载中'}</td>
            <td>
              <input
                type="number"
                name="pingPeriod"
                min="1"
                value={config.pingPeriod}
                onChange={handleConfigChange}
              />
            </td>
          </tr>
        </tbody>
      </table>
      <button onClick={resetConfig}>重置配置</button>
      {loading && <p>更新中...</p>}
      <style>{`
        .sonar-control {
          padding: 20px;
          max-width: 600px;
          margin: 0 auto;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 10px;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
        }
        .error-message {
          color: red;
          margin-bottom: 10px;
        }
        button {
          padding: 8px 16px;
          background-color: #4CAF50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
      `}</style>
    </div>
  );
};

export default SonarControlTable; 