/**
 * 测量组件相关类型定义
 */

import {
  PixelCoordinate,
  MeasurementPoint,
  MeasurementResult,
  SonarMeasurementConfig,
} from '../../lib/sonar-geometry';

// 测量模式
export type MeasurementMode = 'none' | 'point' | 'distance' | 'continuous' | 'angle' | 'segment';

// 测量状态
export interface MeasurementState {
  mode: MeasurementMode;
  isActive: boolean;
  points: MeasurementPoint[];
  results: MeasurementResult[];
  selectedPointId?: string;
  isDragging: boolean;
}

// 测量组件属性
export interface MeasurementOverlayProps {
  enabled: boolean;
  measurementState: MeasurementState;
  onMeasurementStateChange: (state: MeasurementState) => void;
  sonarConfig: SonarMeasurementConfig;
  imageWidth: number;
  imageHeight: number;
  range: number;
  className?: string;
}

// 原点标记器属性
export interface OriginMarkerProps {
  position: PixelCoordinate;
  visible: boolean;
  size?: number;
  color?: string;
  className?: string;
}

// 坐标标注属性
export interface CoordinateLabelProps {
  point: MeasurementPoint;
  format: 'polar' | 'cartesian' | 'both';
  precision: {
    distance: number;
    angle: number;
  };
  position?: 'auto' | 'top' | 'bottom' | 'left' | 'right';
  visible?: boolean;
  className?: string;
}

// 测量点组件属性
export interface MeasurementPointProps {
  point: MeasurementPoint;
  isSelected: boolean;
  isDraggable: boolean;
  onSelect?: (point: MeasurementPoint) => void;
  onDrag?: (point: MeasurementPoint, newPosition: PixelCoordinate) => void;
  onDragStart?: (point: MeasurementPoint) => void;
  onDragEnd?: (point: MeasurementPoint) => void;
  showCoordinates?: boolean;
  coordinateFormat?: 'polar' | 'cartesian' | 'both';
  className?: string;
}

// 测量工具栏属性
export interface MeasurementToolbarProps {
  currentMode: MeasurementMode;
  onModeChange: (mode: MeasurementMode) => void;
  onClearAll: () => void;
  onToggleOriginMarker: () => void;
  onToggleSectorOutline: () => void;
  measurementResults: MeasurementResult[];
  showOriginMarker: boolean;
  showSectorOutline: boolean;
  className?: string;
}

// 测量线组件属性
export interface MeasurementLineProps {
  startPoint: PixelCoordinate;
  endPoint: PixelCoordinate;
  color?: string;
  strokeWidth?: number;
  strokeDashArray?: string;
  showLabel?: boolean;
  labelText?: string;
  className?: string;
}

// 扇形轮廓组件属性
export interface SectorOutlineProps {
  boundaryPoints: PixelCoordinate[];
  visible: boolean;
  color?: string;
  strokeWidth?: number;
  strokeDashArray?: string;
  fillOpacity?: number;
  className?: string;
}

// 测量结果显示属性
export interface MeasurementResultsProps {
  results: MeasurementResult[];
  onResultSelect?: (result: MeasurementResult) => void;
  onResultDelete?: (resultId: string) => void;
  selectedResultId?: string;
  maxResults?: number;
  className?: string;
}

// 鼠标/触摸事件处理
export interface MeasurementEventHandlers {
  onPointerDown: (event: React.PointerEvent, coordinate: PixelCoordinate) => void;
  onPointerMove: (event: React.PointerEvent, coordinate: PixelCoordinate) => void;
  onPointerUp: (event: React.PointerEvent, coordinate: PixelCoordinate) => void;
  onPointerLeave?: (event: React.PointerEvent) => void;
}

// 测量配置选项
export interface MeasurementOptions {
  enableSnapping: boolean;
  snapDistance: number;
  showGrid: boolean;
  gridSpacing: number;
  showRuler: boolean;
  autoSave: boolean;
  maxPoints: number;
  allowDuplicatePoints: boolean;
}

// 导出数据格式
export interface MeasurementExportData {
  timestamp: number;
  sonarConfig: {
    range: number;
    angle: number;
    direction: number;
    imageSize: { width: number; height: number };
    origin: PixelCoordinate;
  };
  measurements: {
    points: MeasurementPoint[];
    results: MeasurementResult[];
  };
  metadata: {
    version: string;
    exportedBy: string;
  };
}

// 测量历史记录
export interface MeasurementHistory {
  id: string;
  timestamp: number;
  action: 'add_point' | 'remove_point' | 'move_point' | 'add_result' | 'remove_result' | 'clear_all';
  data: Record<string, unknown>;
  description: string;
}

// 撤销/重做状态
export interface UndoRedoState {
  history: MeasurementHistory[];
  currentIndex: number;
  canUndo: boolean;
  canRedo: boolean;
}