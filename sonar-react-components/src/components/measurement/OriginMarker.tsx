import React from 'react';
import { Crosshair } from 'lucide-react';
import { OriginMarkerProps } from './types';

/**
 * 声呐原点标记器组件
 * 显示声呐的原点位置，使用十字准星图标
 */
const OriginMarker: React.FC<OriginMarkerProps> = ({
  position,
  visible,
  size = 24,
  color = '#ff6b35',
  className = '',
}) => {
  if (!visible) {
    return null;
  }

  return (
    <div
      className={`absolute pointer-events-none z-40 ${className}`}
      style={{
        left: position.x - size / 2,
        top: position.y - size / 2,
        width: size,
        height: size,
      }}
    >
      {/* 十字准星图标 */}
      <Crosshair
        size={size}
        color={color}
        className="drop-shadow-lg"
        strokeWidth={2}
      />
      
      {/* 半透明圆形背景 */}
      <div
        className="absolute inset-0 rounded-full bg-black/30 backdrop-blur-sm"
        style={{
          transform: 'scale(1.2)',
        }}
      />
      
      {/* 中心小圆点 */}
      <div
        className="absolute rounded-full"
        style={{
          left: '50%',
          top: '50%',
          width: 4,
          height: 4,
          backgroundColor: color,
          transform: 'translate(-50%, -50%)',
          boxShadow: '0 0 4px rgba(0,0,0,0.5)',
        }}
      />
      
      {/* 原点标签 */}
      <div
        className="absolute text-xs font-semibold text-white bg-black/60 px-1 py-0.5 rounded whitespace-nowrap"
        style={{
          left: '50%',
          top: size + 4,
          transform: 'translateX(-50%)',
          fontSize: '10px',
        }}
      >
        原点
      </div>
    </div>
  );
};

export default OriginMarker;