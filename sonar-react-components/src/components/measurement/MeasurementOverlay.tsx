import React, { useCallback, useMemo, useRef } from 'react';
import { MeasurementOverlayProps } from './types';
import { PixelCoordinate, SonarGeometry, MeasurementPoint as MeasurementPointType, calculateDistance } from '../../lib/sonar-geometry';
import OriginMarker from './OriginMarker';
import MeasurementPoint from './MeasurementPoint';

/**
 * 测量覆盖层组件
 * 整合所有测量功能的容器组件
 */
const MeasurementOverlay: React.FC<MeasurementOverlayProps> = ({
  enabled,
  measurementState,
  onMeasurementStateChange,
  sonarConfig,
  imageWidth,
  imageHeight,
  range,
  coordinateTransformer,
  className = '',
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);

  // 简化的坐标系统 - Canvas像素与图像像素 1:1 对应
  // 无需坐标转换，直接使用像素坐标

  // 创建声呐几何计算器
  const sonarGeometry = useMemo(() => {
    return new SonarGeometry(
      range,
      sonarConfig.sonarAngle,
      imageWidth,
      imageHeight,
      sonarConfig.originConfig,
      sonarConfig.sonarDirection
    );
  }, [range, sonarConfig, imageWidth, imageHeight]);

  // 计算连续测距结果的辅助函数
  const calculateContinuousResult = useCallback((points: MeasurementPointType[]) => {
    if (points.length < 2) return null;
    
    let totalDistance = 0;
    let totalPixelDistance = 0;
    
    // 计算所有相邻点之间的距离
    for (let i = 0; i < points.length - 1; i++) {
      const point1 = points[i];
      const point2 = points[i + 1];
      const segmentPixelDistance = calculateDistance(point1.pixelCoordinate, point2.pixelCoordinate);
      const segmentPhysicalDistance = (segmentPixelDistance / imageHeight) * range;
      
      totalPixelDistance += segmentPixelDistance;
      totalDistance += segmentPhysicalDistance;
    }
    
    return {
      id: `continuous_${Date.now()}`,
      type: 'distance' as const,
      points: points,
      value: totalDistance,
      pixelValue: totalPixelDistance,
      description: `连续测距总长度: ${totalDistance.toFixed(2)}m`,
      unit: 'm',
      timestamp: Date.now(),
    };
  }, [imageHeight, range]);

  // 获取原点位置
  const originPosition = useMemo(() => {
    return sonarGeometry.getOriginPixelCoordinate();
  }, [sonarGeometry]);

  // 获取扇形边界点（用于绘制扇形轮廓）
  const sectorBoundary = useMemo(() => {
    if (!sonarConfig.showSectorOutline) return [];
    return sonarGeometry.getSectorBoundary(30);
  }, [sonarGeometry, sonarConfig.showSectorOutline]);

  // 处理点击事件（添加测量点）
  const handleOverlayClick = useCallback((event: React.MouseEvent) => {
    if (measurementState.mode === 'none' || measurementState.isDragging) {
      return;
    }

    event.preventDefault();
    event.stopPropagation();

    // 获取相对于覆盖层的坐标
    const rect = event.currentTarget.getBoundingClientRect();
    const viewportCoordinate: PixelCoordinate = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };

    // 如果有坐标转换器，将视口坐标转换为图像坐标
    const pixelCoordinate: PixelCoordinate = coordinateTransformer
      ? coordinateTransformer.viewportToImage(viewportCoordinate)
      : viewportCoordinate;

    // 调试信息
    if (coordinateTransformer && coordinateTransformer.needsTransform()) {
      console.log('坐标转换调试:', {
        scale: coordinateTransformer.getScale(),
        viewportCoord: viewportCoordinate,
        imageCoord: pixelCoordinate,
        rectSize: { width: rect.width, height: rect.height }
      });
    }

    // 检查点击位置是否在有效区域内
    if (!sonarGeometry.isPointValid(pixelCoordinate)) {
      console.warn('点击位置不在声呐有效区域内');
      return;
    }

    // 创建新的测量点
    const newPoint = sonarGeometry.createMeasurementPoint(pixelCoordinate);
    
    // 更新测量状态
    const newPoints = [...measurementState.points, newPoint];
    const newResults = [...measurementState.results];

    // 根据测量模式处理结果
    if (measurementState.mode === 'point') {
      // 点到原点测量
      const result = sonarGeometry.measurePointToOrigin(newPoint);
      newResults.push(result);
    } else if (measurementState.mode === 'distance' && newPoints.length % 2 === 0) {
      // 两点距离测量 - 每两个点为一组，测量每组内两点之间的距离
      const lastTwoPoints = newPoints.slice(-2);
      const result = sonarGeometry.measureDistance(lastTwoPoints[0], lastTwoPoints[1]);
      newResults.push(result);
    } else if (measurementState.mode === 'continuous' && newPoints.length >= 2) {
      // 连续测距 - 使用辅助函数计算所有线段的总长度
      const continuousResult = calculateContinuousResult(newPoints);
      if (continuousResult) {
        // 替换之前的连续测距结果（如果存在），而不是添加新的
        const existingIndex = measurementState.results.findIndex(r => r.description?.includes('连续测距总长度'));
        if (existingIndex !== -1) {
          // 更新现有的连续测距结果
          const updatedResults = measurementState.results.map((r, index) => 
            index === existingIndex ? continuousResult : r
          );
          newResults.splice(0, newResults.length, ...updatedResults);
        } else {
          // 添加新的连续测距结果
          newResults.push(continuousResult);
        }
      }
    }

    onMeasurementStateChange({
      ...measurementState,
      points: newPoints,
      results: newResults,
    });
  }, [measurementState, onMeasurementStateChange, sonarGeometry, calculateContinuousResult, coordinateTransformer]);

  // 处理测量点选择
  const handlePointSelect = useCallback((selectedPoint: MeasurementPointType) => {
    onMeasurementStateChange({
      ...measurementState,
      selectedPointId: selectedPoint.id,
    });
  }, [measurementState, onMeasurementStateChange]);

  // 处理测量点拖拽
  const handlePointDrag = useCallback((point: MeasurementPointType, newViewportPosition: PixelCoordinate) => {
    // 如果有坐标转换器，将视口坐标转换为图像坐标
    const newImagePosition: PixelCoordinate = coordinateTransformer
      ? coordinateTransformer.viewportToImage(newViewportPosition)
      : newViewportPosition;

    // 检查新位置是否有效
    if (!sonarGeometry.isPointValid(newImagePosition)) {
      return;
    }

    // 创建更新后的测量点
    const updatedPoint = sonarGeometry.createMeasurementPoint(newImagePosition, point.id);
    
    // 更新点列表
    const newPoints = measurementState.points.map(p => 
      p.id === point.id ? updatedPoint : p
    );

    // 如果是连续测距模式，需要重新计算总长度
    const newResults = [...measurementState.results];
    if (measurementState.mode === 'continuous' && newPoints.length >= 2) {
      const continuousResult = calculateContinuousResult(newPoints);
      if (continuousResult) {
        // 查找现有的连续测距结果并替换
        const existingIndex = newResults.findIndex(r => r.description?.includes('连续测距总长度'));
        if (existingIndex !== -1) {
          newResults[existingIndex] = continuousResult;
        } else {
          newResults.push(continuousResult);
        }
      }
    }

    onMeasurementStateChange({
      ...measurementState,
      points: newPoints,
      results: newResults,
    });
  }, [measurementState, onMeasurementStateChange, sonarGeometry, calculateContinuousResult, coordinateTransformer]);

  // 处理拖拽开始
  const handleDragStart = useCallback(() => {
    onMeasurementStateChange({
      ...measurementState,
      isDragging: true,
    });
  }, [measurementState, onMeasurementStateChange]);

  // 处理拖拽结束
  const handleDragEnd = useCallback(() => {
    onMeasurementStateChange({
      ...measurementState,
      isDragging: false,
    });
  }, [measurementState, onMeasurementStateChange]);

  // 渲染测量线
  const renderMeasurementLines = () => {
    const lines: React.ReactElement[] = [];

    // 距离测量线
    if (measurementState.mode === 'distance') {
      for (let i = 0; i < measurementState.points.length - 1; i += 2) {
        const point1 = measurementState.points[i];
        const point2 = measurementState.points[i + 1];
        
        if (point2) {
          // 直接使用图像像素坐标
          const containerPoint1 = point1.pixelCoordinate;
          const containerPoint2 = point2.pixelCoordinate;
          
          lines.push(
            <line
              key={`distance-line-${i}`}
              x1={containerPoint1.x}
              y1={containerPoint1.y}
              x2={containerPoint2.x}
              y2={containerPoint2.y}
              stroke="#3b82f6"
              strokeWidth="2"
              strokeDasharray="5,5"
            />
          );
        }
      }
    }

    // 连续测距线 - 按顺序连接所有点
    if (measurementState.mode === 'continuous') {
      for (let i = 0; i < measurementState.points.length - 1; i++) {
        const point1 = measurementState.points[i];
        const point2 = measurementState.points[i + 1];
        
        // 直接使用图像像素坐标
        const containerPoint1 = point1.pixelCoordinate;
        const containerPoint2 = point2.pixelCoordinate;
        
        lines.push(
          <line
            key={`continuous-line-${i}`}
            x1={containerPoint1.x}
            y1={containerPoint1.y}
            x2={containerPoint2.x}
            y2={containerPoint2.y}
            stroke="#f59e0b"
            strokeWidth="2"
            strokeDasharray="3,3"
          />
        );
      }
    }

    // 点到原点的线
    if (measurementState.mode === 'point') {
      const containerOrigin = originPosition;
      
      measurementState.points.forEach((point, index) => {
        // 直接使用图像像素坐标
        const containerPoint = point.pixelCoordinate;
        
        lines.push(
          <line
            key={`origin-line-${index}`}
            x1={containerOrigin.x}
            y1={containerOrigin.y}
            x2={containerPoint.x}
            y2={containerPoint.y}
            stroke="#10b981"
            strokeWidth="1"
            strokeDasharray="3,3"
            opacity="0.7"
          />
        );
      });
    }

    return lines;
  };

  // 渲染扇形轮廓
  const renderSectorOutline = () => {
    if (!sonarConfig.showSectorOutline || sectorBoundary.length < 3) {
      return null;
    }

    // 直接使用图像像素坐标
    const containerBoundary = sectorBoundary;

    const pathData = containerBoundary
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
      .join(' ') + ' Z';

    return (
      <path
        d={pathData}
        fill="rgba(255, 107, 53, 0.1)"
        stroke="#ff6b35"
        strokeWidth="1"
        strokeDasharray="2,2"
        opacity="0.6"
      />
    );
  };

  if (!enabled) {
    return null;
  }

  return (
    <div
      ref={overlayRef}
      className={`absolute inset-0 pointer-events-auto z-30 ${className}`}
      onClick={handleOverlayClick}
      style={{ 
        cursor: measurementState.mode !== 'none' ? 'crosshair' : 'default',
        backgroundColor: measurementState.mode !== 'none' ? 'rgba(0,0,0,0.01)' : 'transparent',
        width: `${imageWidth}px`,
        height: `${imageHeight}px`
      }}
    >
      {/* SVG 覆盖层用于绘制线条和形状 */}
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 15 }}
      >
        {/* 扇形轮廓 */}
        {renderSectorOutline()}
        
        {/* 测量线 */}
        {renderMeasurementLines()}
      </svg>

      {/* 原点标记 */}
      <OriginMarker
        position={originPosition}
        visible={sonarConfig.showOriginMarker}
        size={24}
        color="#ff6b35"
      />

      {/* 测量点 */}
      {measurementState.points.map((point) => {
        const displayPoint = point;
        
        return (
          <MeasurementPoint
            key={point.id}
            point={displayPoint}
            isSelected={point.id === measurementState.selectedPointId}
            isDraggable={measurementState.isActive}
            onSelect={handlePointSelect}
            onDrag={handlePointDrag}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            showCoordinates={true}
            coordinateFormat={sonarConfig.coordinateFormat}
          />
        );
      })}

    </div>
  );
};

export default MeasurementOverlay;