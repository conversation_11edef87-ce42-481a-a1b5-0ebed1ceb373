import React, { useMemo } from 'react';
import { CoordinateLabelProps } from './types';
import { formatCoordinate } from '../../lib/sonar-geometry';

/**
 * 坐标标注组件
 * 显示测量点的坐标信息（极坐标或直角坐标）
 */
const CoordinateLabel: React.FC<CoordinateLabelProps> = ({
  point,
  format,
  precision,
  position = 'auto',
  visible = true,
  className = '',
}) => {
  // 计算标注位置
  const labelPosition = useMemo(() => {
    const { pixelCoordinate } = point;
    const offset = 8; // 标注与点的偏移距离
    
    if (position === 'auto') {
      // 自动选择最佳位置，避免边界溢出
      return {
        left: pixelCoordinate.x + offset,
        top: pixelCoordinate.y - offset,
        transform: 'translateY(-100%)',
      };
    }
    
    switch (position) {
      case 'top':
        return {
          left: pixelCoordinate.x,
          top: pixelCoordinate.y - offset,
          transform: 'translate(-50%, -100%)',
        };
      case 'bottom':
        return {
          left: pixelCoordinate.x,
          top: pixelCoordinate.y + offset,
          transform: 'translateX(-50%)',
        };
      case 'left':
        return {
          left: pixelCoordinate.x - offset,
          top: pixelCoordinate.y,
          transform: 'translate(-100%, -50%)',
        };
      case 'right':
        return {
          left: pixelCoordinate.x + offset,
          top: pixelCoordinate.y,
          transform: 'translateY(-50%)',
        };
      default:
        return {
          left: pixelCoordinate.x + offset,
          top: pixelCoordinate.y - offset,
          transform: 'translateY(-100%)',
        };
    }
  }, [point, position]);

  // 格式化坐标文本
  const coordinateText = useMemo(() => {
    const lines: string[] = [];
    
    if (format === 'polar' || format === 'both') {
      const polarText = formatCoordinate(point.polarCoordinate, 'polar', precision);
      lines.push(polarText);
    }
    
    if (format === 'cartesian' || format === 'both') {
      const cartesianText = formatCoordinate(point.cartesianCoordinate, 'cartesian', precision);
      lines.push(cartesianText);
    }
    
    return lines;
  }, [point, format, precision]);

  // 有效性指示器
  const validityClass = point.isValid 
    ? 'border-green-500 bg-green-900/80 text-green-100' 
    : 'border-red-500 bg-red-900/80 text-red-100';

  if (!visible) {
    return null;
  }

  return (
    <div
      className={`absolute z-30 pointer-events-none ${className}`}
      style={labelPosition}
    >
      <div className={`
        px-2 py-1 rounded-md text-xs font-mono border backdrop-blur-sm
        shadow-lg whitespace-nowrap
        ${validityClass}
      `}>
        {coordinateText.map((line, index) => (
          <div key={index} className="leading-tight">
            {line}
          </div>
        ))}
        
        {/* 点ID显示 */}
        <div className="text-xs opacity-75 mt-0.5 font-sans">
          ID: {point.id.split('_')[1]?.substring(0, 4) || 'N/A'}
        </div>
      </div>
      
    </div>
  );
};

export default CoordinateLabel;