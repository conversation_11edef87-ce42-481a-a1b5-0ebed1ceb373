/**
 * 声呐几何测量相关类型定义
 */

// 像素坐标
export interface PixelCoordinate {
  x: number;
  y: number;
}

// 极坐标（距离+角度）
export interface PolarCoordinate {
  distance: number; // 距离（米）
  angle: number;    // 角度（度）相对于声呐正前方
}

// 直角坐标（物理单位）
export interface CartesianCoordinate {
  x: number; // 水平距离（米）
  y: number; // 垂直距离（米）
}

// 声呐原点配置
export interface SonarOriginConfig {
  preset: 'top-center' | 'bottom-center' | 'custom';
  customPosition?: PixelCoordinate;
}

// 声呐参数配置
export interface SonarParameters {
  range: number;        // 量程（米）
  angle: number;        // 开角（度）
  direction: number;    // 正前方向角度（度，0度为垂直向下）
  imageWidth: number;   // 图像宽度（像素）
  imageHeight: number;  // 图像高度（像素）
  origin: PixelCoordinate; // 原点像素位置
}

// 测量点数据
export interface MeasurementPoint {
  id: string;
  pixelCoordinate: PixelCoordinate;
  polarCoordinate: PolarCoordinate;
  cartesianCoordinate: CartesianCoordinate;
  timestamp: number;
  isValid: boolean; // 是否在有效扇形区域内
}

// 测量类型
export type MeasurementType = 'point' | 'distance' | 'angle' | 'segment';

// 测量结果
export interface MeasurementResult {
  id: string;
  type: MeasurementType;
  points: MeasurementPoint[];
  value: number;
  unit: string;
  description: string;
  timestamp: number;
}

// 测量配置
export interface SonarMeasurementConfig {
  enabled: boolean;
  originConfig: SonarOriginConfig;
  sonarAngle: number;
  sonarDirection: number;
  showOriginMarker: boolean;
  showSectorOutline: boolean;
  coordinateFormat: 'polar' | 'cartesian' | 'both';
  precision: {
    distance: number;  // 距离精度（小数位）
    angle: number;     // 角度精度（小数位）
  };
}

// 扇形参数
export interface SectorParameters {
  origin: PixelCoordinate;
  radius: number;      // 扇形半径（像素）
  startAngle: number;  // 起始角度（弧度）
  endAngle: number;    // 结束角度（弧度）
  direction: number;   // 扇形朝向（弧度）
}

// 坐标转换结果
export interface CoordinateConversionResult {
  pixel: PixelCoordinate;
  polar: PolarCoordinate;
  cartesian: CartesianCoordinate;
  isValid: boolean;
}