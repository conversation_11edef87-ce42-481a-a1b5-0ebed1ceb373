/**
 * 扇形区域验证功能
 */

import {
  PixelCoordinate,
  SonarParameters,
  SectorParameters,
} from './types';
import {
  calculateDistance,
  calculateAngle,
  degreesToRadians,
  radiansToDegrees,
} from './CoordinateSystem';

/**
 * 根据声呐参数创建扇形参数
 */
export function createSectorParameters(sonarParams: SonarParameters): SectorParameters {
  const { origin, angle, direction, imageHeight } = sonarParams;
  
  // 扇形半径为图像高度（覆盖整个量程）
  const radius = imageHeight;
  
  // 直接使用声呐方向角度（屏幕坐标系）
  // 270° = 向上, 0° = 向右, 90° = 向下, 180° = 向左
  const directionRadians = degreesToRadians(direction);
  const halfAngleRadians = degreesToRadians(angle / 2);
  
  // 计算扇形的起始和结束角度
  const startAngle = directionRadians - halfAngleRadians;
  const endAngle = directionRadians + halfAngleRadians;
  
  
  return {
    origin,
    radius,
    startAngle,
    endAngle,
    direction: directionRadians,
  };
}

/**
 * 规范化角度到 [0, 2π) 范围
 */
export function normalizeAngle(angle: number): number {
  while (angle < 0) angle += 2 * Math.PI;
  while (angle >= 2 * Math.PI) angle -= 2 * Math.PI;
  return angle;
}

/**
 * 规范化角度到 [-π, π) 范围
 */
export function normalizeAngleSymmetric(angle: number): number {
  while (angle < -Math.PI) angle += 2 * Math.PI;
  while (angle >= Math.PI) angle -= 2 * Math.PI;
  return angle;
}

/**
 * 检查角度是否在给定范围内（处理跨越0度的情况）
 */
export function isAngleInRange(angle: number, startAngle: number, endAngle: number): boolean {
  // 规范化所有角度到 [-π, π) 范围
  const normalizedAngle = normalizeAngleSymmetric(angle);
  const normalizedStart = normalizeAngleSymmetric(startAngle);
  const normalizedEnd = normalizeAngleSymmetric(endAngle);
  
  if (normalizedStart <= normalizedEnd) {
    // 正常情况：startAngle < endAngle
    return normalizedAngle >= normalizedStart && normalizedAngle <= normalizedEnd;
  } else {
    // 跨越-π/π边界的情况
    return normalizedAngle >= normalizedStart || normalizedAngle <= normalizedEnd;
  }
}

/**
 * 检查点是否在扇形区域内
 */
export function isPointInSector(
  point: PixelCoordinate,
  sectorParams: SectorParameters
): boolean {
  const { origin, radius, startAngle, endAngle } = sectorParams;
  
  // 检查距离是否在范围内
  const distance = calculateDistance(origin, point);
  if (distance > radius) {
    return false;
  }
  
  // 如果点就是原点，认为是有效的
  if (distance === 0) {
    return true;
  }
  
  // 检查角度是否在扇形范围内
  const pointAngle = calculateAngle(origin, point);
  return isAngleInRange(pointAngle, startAngle, endAngle);
}

/**
 * 检查点是否在声呐有效区域内
 */
export function isPointInSonarSector(
  point: PixelCoordinate,
  sonarParams: SonarParameters
): boolean {
  const sectorParams = createSectorParameters(sonarParams);
  return isPointInSector(point, sectorParams);
}

/**
 * 计算点到扇形边界的最短距离
 */
export function distanceToSectorBoundary(
  point: PixelCoordinate,
  sectorParams: SectorParameters
): number {
  const { origin, radius, startAngle, endAngle } = sectorParams;
  
  const pointDistance = calculateDistance(origin, point);
  const pointAngle = calculateAngle(origin, point);
  
  // 如果点在扇形内，返回0
  if (isPointInSector(point, sectorParams)) {
    return 0;
  }
  
  let minDistance = Infinity;
  
  // 检查到半径边界的距离
  if (pointDistance > radius) {
    minDistance = Math.min(minDistance, pointDistance - radius);
  }
  
  // 检查到角度边界的距离
  if (!isAngleInRange(pointAngle, startAngle, endAngle)) {
    // 计算到起始角度线的距离
    const startAngleDistance = Math.abs(normalizeAngleSymmetric(pointAngle - startAngle)) * pointDistance;
    const endAngleDistance = Math.abs(normalizeAngleSymmetric(pointAngle - endAngle)) * pointDistance;
    
    minDistance = Math.min(minDistance, Math.min(startAngleDistance, endAngleDistance));
  }
  
  return minDistance;
}

/**
 * 获取扇形边界上的点（用于绘制扇形轮廓）
 */
export function getSectorBoundaryPoints(
  sectorParams: SectorParameters,
  pointCount: number = 50
): PixelCoordinate[] {
  const { origin, radius, startAngle, endAngle } = sectorParams;
  const points: PixelCoordinate[] = [];
  
  // 添加原点
  points.push(origin);
  
  // 生成弧线上的点
  const angleStep = (endAngle - startAngle) / (pointCount - 1);
  for (let i = 0; i < pointCount; i++) {
    const angle = startAngle + i * angleStep;
    const x = origin.x + radius * Math.cos(angle);
    const y = origin.y + radius * Math.sin(angle);
    points.push({ x, y });
  }
  
  // 回到原点闭合扇形
  points.push(origin);
  
  return points;
}

/**
 * 检查线段是否完全在扇形内
 */
export function isLineSegmentInSector(
  start: PixelCoordinate,
  end: PixelCoordinate,
  sectorParams: SectorParameters,
  checkPoints: number = 10
): boolean {
  // 检查端点
  if (!isPointInSector(start, sectorParams) || !isPointInSector(end, sectorParams)) {
    return false;
  }
  
  // 检查线段上的中间点
  for (let i = 1; i < checkPoints - 1; i++) {
    const t = i / (checkPoints - 1);
    const point: PixelCoordinate = {
      x: start.x + t * (end.x - start.x),
      y: start.y + t * (end.y - start.y),
    };
    
    if (!isPointInSector(point, sectorParams)) {
      return false;
    }
  }
  
  return true;
}

/**
 * 获取扇形的有效角度范围（度）
 */
export function getSectorAngleRange(sectorParams: SectorParameters): { start: number; end: number; span: number } {
  const { startAngle, endAngle } = sectorParams;
  
  const startDegrees = radiansToDegrees(startAngle);
  const endDegrees = radiansToDegrees(endAngle);
  
  let span = endDegrees - startDegrees;
  if (span < 0) span += 360;
  
  return {
    start: startDegrees,
    end: endDegrees,
    span,
  };
}