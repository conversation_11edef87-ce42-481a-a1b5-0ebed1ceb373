/**
 * 声呐几何计算库导出
 */

// 类型定义
export type {
  PixelCoordinate,
  PolarCoordinate,
  CartesianCoordinate,
  SonarOriginConfig,
  SonarParameters,
  MeasurementPoint,
  MeasurementResult,
  MeasurementType,
  SonarMeasurementConfig,
  SectorParameters,
  CoordinateConversionResult,
} from './types';

// 坐标系统转换
export {
  degreesToRadians,
  radiansToDegrees,
  calculateDistance,
  calculateAngle,
  calculateOriginPosition,
  pixelToPolarCoordinate,
  polarToPixelCoordinate,
  polarToCartesianCoordinate,
  cartesianToPolarCoordinate,
  pixelToCartesianCoordinate,
  convertCoordinates,
  formatCoordinate,
} from './CoordinateSystem';

// 扇形区域验证
export {
  createSectorParameters,
  normalizeAngle,
  normalizeAngleSymmetric,
  isAngleInRange,
  isPointInSector,
  isPointInSonarSector,
  distanceToSectorBoundary,
  getSectorBoundaryPoints,
  isLineSegmentInSector,
  getSectorAngleRange,
} from './SectorValidation';

// 主要几何计算类
export { SonarGeometry } from './SonarGeometry';