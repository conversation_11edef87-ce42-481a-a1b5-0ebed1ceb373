/**
 * 坐标系统转换函数
 */

import {
  PixelCoordinate,
  PolarCoordinate,
  CartesianCoordinate,
  SonarParameters,
  CoordinateConversionResult,
} from './types';

/**
 * 角度转换：度 -> 弧度
 */
export function degreesToRadians(degrees: number): number {
  return (degrees * Math.PI) / 180;
}

/**
 * 角度转换：弧度 -> 度
 */
export function radiansToDegrees(radians: number): number {
  return (radians * 180) / Math.PI;
}

/**
 * 计算两点之间的距离
 */
export function calculateDistance(p1: PixelCoordinate, p2: PixelCoordinate): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 计算两点之间的角度（弧度）
 * 注意：这里使用屏幕坐标系统，Y轴向下为正
 * 角度定义：0° = 向右，90° = 向下，-90° = 向上
 */
export function calculateAngle(from: PixelCoordinate, to: PixelCoordinate): number {
  const dx = to.x - from.x;
  const dy = to.y - from.y;
  
  // 使用标准的 atan2，在屏幕坐标系中：
  // 0° = 向右, 90° = 向下, -90° = 向上, ±180° = 向左
  return Math.atan2(dy, dx);
}

/**
 * 根据声呐参数计算原点位置
 */
export function calculateOriginPosition(
  preset: 'top-center' | 'bottom-center' | 'custom',
  imageWidth: number,
  imageHeight: number,
  customPosition?: PixelCoordinate
): PixelCoordinate {
  switch (preset) {
    case 'top-center':
      return { x: imageWidth / 2, y: 0 };
    case 'bottom-center':
      return { x: imageWidth / 2, y: imageHeight - 1 };
    case 'custom':
      return customPosition || { x: imageWidth / 2, y: 0 };
    default:
      return { x: imageWidth / 2, y: 0 };
  }
}

/**
 * 像素坐标转换为极坐标
 */
export function pixelToPolarCoordinate(
  pixelCoordinate: PixelCoordinate,
  sonarParams: SonarParameters
): PolarCoordinate {
  const { origin, range, imageHeight, direction } = sonarParams;
  
  // 计算相对于原点的像素距离
  const pixelDistance = calculateDistance(origin, pixelCoordinate);
  
  // 转换为物理距离（米）
  const physicalDistance = (pixelDistance / imageHeight) * range;
  
  // 计算角度（相对于声呐正前方）
  const angleFromOrigin = calculateAngle(origin, pixelCoordinate);
  const directionRadians = degreesToRadians(direction);
  let relativeAngle = angleFromOrigin - directionRadians;
  
  // 规范化角度到 -π 到 π 范围
  while (relativeAngle > Math.PI) relativeAngle -= 2 * Math.PI;
  while (relativeAngle < -Math.PI) relativeAngle += 2 * Math.PI;
  
  // 转换为度
  const angleDegrees = radiansToDegrees(relativeAngle);
  
  return {
    distance: physicalDistance,
    angle: angleDegrees,
  };
}

/**
 * 极坐标转换为像素坐标
 */
export function polarToPixelCoordinate(
  polarCoordinate: PolarCoordinate,
  sonarParams: SonarParameters
): PixelCoordinate {
  const { origin, range, imageHeight, direction } = sonarParams;
  const { distance, angle } = polarCoordinate;
  
  // 转换为像素距离
  const pixelDistance = (distance / range) * imageHeight;
  
  // 计算实际角度（加上声呐方向）
  const directionRadians = degreesToRadians(direction);
  const angleRadians = degreesToRadians(angle);
  const actualAngle = angleRadians + directionRadians;
  
  // 计算像素坐标
  const x = origin.x + pixelDistance * Math.cos(actualAngle);
  const y = origin.y + pixelDistance * Math.sin(actualAngle);
  
  return { x, y };
}

/**
 * 极坐标转换为直角坐标
 */
export function polarToCartesianCoordinate(
  polarCoordinate: PolarCoordinate
): CartesianCoordinate {
  const { distance, angle } = polarCoordinate;
  const angleRadians = degreesToRadians(angle);
  
  return {
    x: distance * Math.sin(angleRadians), // 水平距离（左负右正）
    y: distance * Math.cos(angleRadians), // 垂直距离（前正后负）
  };
}

/**
 * 直角坐标转换为极坐标
 */
export function cartesianToPolarCoordinate(
  cartesianCoordinate: CartesianCoordinate
): PolarCoordinate {
  const { x, y } = cartesianCoordinate;
  
  const distance = Math.sqrt(x * x + y * y);
  const angle = radiansToDegrees(Math.atan2(x, y));
  
  return { distance, angle };
}

/**
 * 像素坐标转换为直角坐标
 */
export function pixelToCartesianCoordinate(
  pixelCoordinate: PixelCoordinate,
  sonarParams: SonarParameters
): CartesianCoordinate {
  const polar = pixelToPolarCoordinate(pixelCoordinate, sonarParams);
  return polarToCartesianCoordinate(polar);
}

/**
 * 完整的坐标转换（从像素坐标开始）
 */
export function convertCoordinates(
  pixelCoordinate: PixelCoordinate,
  sonarParams: SonarParameters
): CoordinateConversionResult {
  const polar = pixelToPolarCoordinate(pixelCoordinate, sonarParams);
  const cartesian = polarToCartesianCoordinate(polar);
  
  // 简单的有效性检查（可以通过 SectorValidation 进一步验证）
  const isValid = polar.distance >= 0 && polar.distance <= sonarParams.range;
  
  return {
    pixel: pixelCoordinate,
    polar,
    cartesian,
    isValid,
  };
}

/**
 * 格式化坐标显示
 */
export function formatCoordinate(
  coordinate: PolarCoordinate | CartesianCoordinate,
  type: 'polar' | 'cartesian',
  precision: { distance: number; angle: number }
): string {
  if (type === 'polar') {
    const polar = coordinate as PolarCoordinate;
    return `(r=${polar.distance.toFixed(precision.distance)}m, θ=${polar.angle.toFixed(precision.angle)}°)`;
  } else {
    const cartesian = coordinate as CartesianCoordinate;
    return `(x=${cartesian.x.toFixed(precision.distance)}m, y=${cartesian.y.toFixed(precision.distance)}m)`;
  }
}