/**
 * 声呐几何计算主类
 */

import {
  PixelCoordinate,
  PolarCoordinate,
  CartesianCoordinate,
  SonarParameters,
  MeasurementPoint,
  MeasurementResult,
  SonarOriginConfig,
} from './types';

import {
  calculateOriginPosition,
  convertCoordinates,
  calculateDistance,
  formatCoordinate,
} from './CoordinateSystem';

import {
  isPointInSonarSector,
  createSectorParameters,
  getSectorBoundaryPoints,
  isLineSegmentInSector,
} from './SectorValidation';

/**
 * 声呐几何计算器类
 */
export class SonarGeometry {
  private sonarParams: SonarParameters;

  constructor(
    range: number,
    angle: number,
    imageWidth: number,
    imageHeight: number,
    originConfig: SonarOriginConfig,
    direction: number = 90 // 默认垂直向下
  ) {
    const origin = calculateOriginPosition(
      originConfig.preset,
      imageWidth,
      imageHeight,
      originConfig.customPosition
    );

    this.sonarParams = {
      range,
      angle,
      direction,
      imageWidth,
      imageHeight,
      origin,
    };
  }

  /**
   * 更新声呐参数
   */
  updateParameters(params: Partial<SonarParameters>): void {
    this.sonarParams = { ...this.sonarParams, ...params };
  }

  /**
   * 获取当前声呐参数
   */
  getParameters(): SonarParameters {
    return { ...this.sonarParams };
  }

  /**
   * 创建测量点
   */
  createMeasurementPoint(
    pixelCoordinate: PixelCoordinate,
    id?: string
  ): MeasurementPoint {
    const conversion = convertCoordinates(pixelCoordinate, this.sonarParams);
    const isValid = isPointInSonarSector(pixelCoordinate, this.sonarParams);

    return {
      id: id || `point_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      pixelCoordinate,
      polarCoordinate: conversion.polar,
      cartesianCoordinate: conversion.cartesian,
      timestamp: Date.now(),
      isValid,
    };
  }

  /**
   * 检查点是否在有效区域内
   */
  isPointValid(pixelCoordinate: PixelCoordinate): boolean {
    return isPointInSonarSector(pixelCoordinate, this.sonarParams);
  }

  /**
   * 计算两点之间的距离测量
   */
  measureDistance(point1: MeasurementPoint, point2: MeasurementPoint): MeasurementResult {
    const pixelDistance = calculateDistance(point1.pixelCoordinate, point2.pixelCoordinate);
    const physicalDistance = (pixelDistance / this.sonarParams.imageHeight) * this.sonarParams.range;

    return {
      id: `distance_${Date.now()}`,
      type: 'distance',
      points: [point1, point2],
      value: physicalDistance,
      unit: 'm',
      description: `两点间距: ${physicalDistance.toFixed(2)}m`,
      timestamp: Date.now(),
    };
  }

  /**
   * 计算点到原点的距离和角度
   */
  measurePointToOrigin(point: MeasurementPoint): MeasurementResult {
    return {
      id: `point_${Date.now()}`,
      type: 'point',
      points: [point],
      value: point.polarCoordinate.distance,
      unit: 'm',
      description: `距离: ${point.polarCoordinate.distance.toFixed(2)}m, 角度: ${point.polarCoordinate.angle.toFixed(1)}°`,
      timestamp: Date.now(),
    };
  }

  /**
   * 计算两条线段的夹角
   */
  measureAngleBetweenSegments(
    line1Point1: MeasurementPoint,
    line1Point2: MeasurementPoint,
    line2Point1: MeasurementPoint,
    line2Point2: MeasurementPoint
  ): MeasurementResult {
    // 计算两条线段的方向向量
    const vector1 = {
      x: line1Point2.cartesianCoordinate.x - line1Point1.cartesianCoordinate.x,
      y: line1Point2.cartesianCoordinate.y - line1Point1.cartesianCoordinate.y,
    };

    const vector2 = {
      x: line2Point2.cartesianCoordinate.x - line2Point1.cartesianCoordinate.x,
      y: line2Point2.cartesianCoordinate.y - line2Point1.cartesianCoordinate.y,
    };

    // 计算向量的模长
    const magnitude1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const magnitude2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);

    // 计算点积
    const dotProduct = vector1.x * vector2.x + vector1.y * vector2.y;

    // 计算夹角（弧度）
    const angleRadians = Math.acos(dotProduct / (magnitude1 * magnitude2));
    const angleDegrees = (angleRadians * 180) / Math.PI;

    return {
      id: `angle_${Date.now()}`,
      type: 'angle',
      points: [line1Point1, line1Point2, line2Point1, line2Point2],
      value: angleDegrees,
      unit: '°',
      description: `线段夹角: ${angleDegrees.toFixed(1)}°`,
      timestamp: Date.now(),
    };
  }

  /**
   * 测量线段长度
   */
  measureSegmentLength(point1: MeasurementPoint, point2: MeasurementPoint): MeasurementResult {
    const distance = Math.sqrt(
      Math.pow(point2.cartesianCoordinate.x - point1.cartesianCoordinate.x, 2) +
      Math.pow(point2.cartesianCoordinate.y - point1.cartesianCoordinate.y, 2)
    );

    return {
      id: `segment_${Date.now()}`,
      type: 'segment',
      points: [point1, point2],
      value: distance,
      unit: 'm',
      description: `线段长度: ${distance.toFixed(2)}m`,
      timestamp: Date.now(),
    };
  }

  /**
   * 获取扇形边界点（用于绘制）
   */
  getSectorBoundary(pointCount: number = 50): PixelCoordinate[] {
    const sectorParams = createSectorParameters(this.sonarParams);
    return getSectorBoundaryPoints(sectorParams, pointCount);
  }

  /**
   * 检查线段是否完全在扇形内
   */
  isSegmentValid(point1: PixelCoordinate, point2: PixelCoordinate): boolean {
    const sectorParams = createSectorParameters(this.sonarParams);
    return isLineSegmentInSector(point1, point2, sectorParams);
  }

  /**
   * 获取格式化的坐标显示
   */
  formatCoordinateDisplay(
    coordinate: PolarCoordinate | CartesianCoordinate,
    type: 'polar' | 'cartesian',
    precision: { distance: number; angle: number } = { distance: 2, angle: 1 }
  ): string {
    return formatCoordinate(coordinate, type, precision);
  }

  /**
   * 获取原点像素坐标
   */
  getOriginPixelCoordinate(): PixelCoordinate {
    return this.sonarParams.origin;
  }

  /**
   * 获取扇形角度范围信息
   */
  getSectorInfo(): {
    angle: number;
    direction: number;
    startAngle: number;
    endAngle: number;
  } {
    const { angle, direction } = this.sonarParams;
    const halfAngle = angle / 2;
    
    return {
      angle,
      direction,
      startAngle: direction - halfAngle,
      endAngle: direction + halfAngle,
    };
  }

  /**
   * 计算比例尺（米/像素）
   */
  getScale(): number {
    return this.sonarParams.range / this.sonarParams.imageHeight;
  }

  /**
   * 获取量程信息
   */
  getRangeInfo(): {
    range: number;
    pixelToMeterRatio: number;
    meterToPixelRatio: number;
  } {
    const scale = this.getScale();
    
    return {
      range: this.sonarParams.range,
      pixelToMeterRatio: scale,
      meterToPixelRatio: 1 / scale,
    };
  }

  /**
   * 像素距离转换为物理距离
   */
  pixelToPhysicalDistance(pixelDistance: number): number {
    return pixelDistance * this.getScale();
  }

  /**
   * 物理距离转换为像素距离
   */
  physicalToPixelDistance(physicalDistance: number): number {
    return physicalDistance / this.getScale();
  }
}