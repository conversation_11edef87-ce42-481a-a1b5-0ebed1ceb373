import { useState, useRef, useCallback } from 'react';
import axios from 'axios';

export interface VideoRecorderState {
  isRecording: boolean;
  isSupported: boolean;
  duration: number;
  error: string | null;
  isPaused: boolean;
}

export interface VideoRecorderOptions {
  mimeType?: string;
  videoBitsPerSecond?: number;
  audioBitsPerSecond?: number;
  sonarIP?: string;
}

export interface VideoRecorderHook extends VideoRecorderState {
  startRecording: (element: HTMLElement) => Promise<boolean>;
  stopRecording: () => Promise<Blob | null>;
  downloadRecording: (blob: Blob, filename?: string) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
}

/**
 * 根据 mimeType 获取对应的文件扩展名
 */
const getExtensionFromMimeType = (mimeType: string): string => {
  if (mimeType.includes('mp4')) return '.mp4';
  if (mimeType.includes('webm')) return '.webm';
  return '.webm'; // 默认
};

/**
 * 获取浏览器支持的 MIME 类型
 */
const getSupportedMimeType = (): string => {
  const types = [
    'video/webm;codecs=vp9',
    'video/webm;codecs=vp8',
    'video/mp4;codecs=h264',
    'video/mp4;codecs=avc1.42E01F', // Firefox H.264 Constrained Baseline Profile
    'video/webm;codecs=h264',
    'video/webm;codecs=avc1',
    'video/mp4',
    'video/webm'
  ];
  
  // 调试信息：显示所有支持的格式
  console.log('浏览器支持的录制格式:');
  types.forEach(type => {
    const supported = MediaRecorder.isTypeSupported(type);
    console.log(`  ${type}: ${supported ? '✓' : '✗'}`);
  });
  
  return types.find(type => MediaRecorder.isTypeSupported(type)) || 'video/webm';
};

/**
 * 获取设备ID
 */
const fetchDeviceId = async (sonarIP: string): Promise<number | null> => {
  try {
    const response = await axios.get(`http://${sonarIP}/api/v1/hello`, {
      timeout: 1000, // 1秒超时
    });
    return response.data.deviceId || null;
  } catch (error) {
    console.warn('获取设备ID失败:', error);
    return null;
  }
};

/**
 * 视频录制 Hook
 * 使用 MediaRecorder API 录制 HTML 元素为视频
 */
export function useVideoRecorder(options: VideoRecorderOptions = {}): VideoRecorderHook {
  const [isRecording, setIsRecording] = useState(false);
  const [duration, setDuration] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 检查浏览器支持
  const isSupported = typeof MediaRecorder !== 'undefined' && 
                     typeof (HTMLCanvasElement.prototype as { captureStream?: () => MediaStream }).captureStream === 'function';

  // 开始录制
  const startRecording = useCallback(async (element: HTMLElement): Promise<boolean> => {
    if (!isSupported) {
      setError('当前浏览器不支持视频录制功能');
      return false;
    }

    if (isRecording) {
      setError('录制已在进行中');
      return false;
    }

    try {
      setError(null);
      let stream: MediaStream;

      // 检查元素类型并获取流
      if (element instanceof HTMLVideoElement) {
        // 如果是 video 元素，直接捕获流
        const videoElement = element as HTMLVideoElement & { captureStream?: () => MediaStream };
        if (videoElement.captureStream) {
          stream = videoElement.captureStream();
        } else {
          throw new Error('Video 元素不支持 captureStream');
        }
      } else if (element instanceof HTMLCanvasElement) {
        // 如果是 canvas 元素，使用 captureStream
        stream = element.captureStream(30); // 30 FPS
      } else {
        // 对于其他元素，使用 getDisplayMedia（需要用户选择）
        // 注意：这种方式需要用户手动选择要录制的区域
        throw new Error('暂不支持录制此类型的元素，请使用 video 或 canvas 元素');
      }

      if (!stream || stream.getVideoTracks().length === 0) {
        throw new Error('无法获取视频流');
      }

      streamRef.current = stream;

      // 设置 MediaRecorder 选项
      const recorderOptions: MediaRecorderOptions = {
        videoBitsPerSecond: options.videoBitsPerSecond || 2500000, // 2.5 Mbps
      };

      // 检查支持的 MIME 类型
      if (options.mimeType && MediaRecorder.isTypeSupported(options.mimeType)) {
        recorderOptions.mimeType = options.mimeType;
      } else {
        if (options.mimeType) {
          console.warn(`MIME 类型 ${options.mimeType} 不被支持，使用兼容的类型`);
        }
        recorderOptions.mimeType = getSupportedMimeType();
      }

      // 创建 MediaRecorder
      try {
        mediaRecorderRef.current = new MediaRecorder(stream, recorderOptions);
        chunksRef.current = [];
      } catch (err) {
        console.error('MediaRecorder 创建失败:', err);
        console.log('尝试使用的 MIME 类型:', recorderOptions.mimeType);
        throw new Error(`录制初始化失败: ${err instanceof Error ? err.message : '未知错误'}`);
      }

      // 设置事件监听器
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onerror = (event) => {
        console.error('MediaRecorder 错误:', event);
        const errorMsg = event.error ? 
          `录制错误: ${event.error.message}` : 
          '录制过程中发生未知错误';
        setError(errorMsg);
        setIsRecording(false);
      };

      // 开始录制
      mediaRecorderRef.current.start(1000); // 每秒收集一次数据
      setIsRecording(true);

      // 开始计时器
      timerRef.current = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);
      
      setIsPaused(false);

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '录制启动失败';
      setError(errorMessage);
      console.error('录制启动失败:', err);
      return false;
    }
  }, [isSupported, isRecording, options]);

  // 停止录制
  const stopRecording = useCallback(async (): Promise<Blob | null> => {
    if (!isRecording || !mediaRecorderRef.current) {
      return null;
    }

    return new Promise((resolve) => {
      const mediaRecorder = mediaRecorderRef.current!;
      
      mediaRecorder.onstop = () => {
        // 清理定时器
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }

        // 停止流
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }

        // 创建最终的 Blob
        const mimeType = mediaRecorder.mimeType || 'video/webm';
        const blob = new Blob(chunksRef.current, { type: mimeType });
        
        // 重置状态
        setIsRecording(false);
        setDuration(0);
        setIsPaused(false);
        chunksRef.current = [];
        mediaRecorderRef.current = null;

        resolve(blob);
      };

      mediaRecorder.stop();
    });
  }, [isRecording]);

  // 下载录制的视频
  const downloadRecording = useCallback(async (blob: Blob, filename?: string) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    const extension = getExtensionFromMimeType(blob.type);
    
    // 如果没有提供文件名，则尝试获取设备ID并生成文件名
    if (!filename) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      
      if (options.sonarIP) {
        // 尝试获取设备ID
        const deviceId = await fetchDeviceId(options.sonarIP);
        filename = deviceId 
          ? `sonar-recording-${deviceId}-${timestamp}${extension}`
          : `sonar-recording-${timestamp}${extension}`;
      } else {
        filename = `sonar-recording-${timestamp}${extension}`;
      }
    }
    
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [options.sonarIP]);

  // 暂停计时器
  const pauseTimer = useCallback(() => {
    if (isRecording && !isPaused && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setIsPaused(true);
    }
  }, [isRecording, isPaused]);

  // 恢复计时器
  const resumeTimer = useCallback(() => {
    if (isRecording && isPaused) {
      timerRef.current = setInterval(() => {
        setDuration(prev => prev + 1);
      }, 1000);
      setIsPaused(false);
    }
  }, [isRecording, isPaused]);

  return {
    isRecording,
    isSupported,
    duration,
    error,
    isPaused,
    startRecording,
    stopRecording,
    downloadRecording,
    pauseTimer,
    resumeTimer,
  };
}