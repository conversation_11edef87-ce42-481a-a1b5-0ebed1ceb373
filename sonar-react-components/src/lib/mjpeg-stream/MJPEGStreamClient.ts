import {
  StreamOptions,
  StreamCallbacks,
  FrameData,
  StreamMetadata,
  StreamStatus,
  LogLevel,
  StatusSubscriber
} from './types';

/**
 * MJPEG 流客户端类
 * 用于处理 MJPEG 流的连接、解析和帧处理
 */
export class MJPEGStreamClient<T extends StreamMetadata = StreamMetadata> {
  private options: Required<StreamOptions>;
  private callbacks: StreamCallbacks<T>;
  private abortController: AbortController | null = null;
  private buffer: Uint8Array = new Uint8Array(0);
  private frameCount: number = 0;
  private _status: StreamStatus = StreamStatus.STANDBY;
  private _error: string | null = null;
  private reconnectAttempts: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private lastFrameTime: number = 0;
  private connectionMonitorTimer: NodeJS.Timeout | null = null;
  private lastMetadata: T | null = null;
  private statusSubscribers: StatusSubscriber[] = [];
  private actualBoundary: string | null = null;

  // 默认选项值
  private static readonly DEFAULT_OPTIONS: Partial<StreamOptions> = {
    contentTypeMarker: 'Content-Type: image/jpeg',
    debug: false,
    headers: {
      'Accept': 'multipart/x-mixed-replace'
    },
    metadataExtractor: () => ({}),
    reconnectDelay: 500,
    maxReconnectAttempts: 5,
    frameTimeoutMultiplier: 5,
    connectionMonitorInterval: 1000,
    autoStart: false,
  };

  /**
   * 构造函数
   * @param options 流配置选项
   * @param callbacks 事件回调
   */
  constructor(options: StreamOptions, callbacks: StreamCallbacks<T> = {}) {
    // 合并默认选项
    this.options = {
      ...MJPEGStreamClient.DEFAULT_OPTIONS,
      ...options
    } as Required<StreamOptions>;

    this.callbacks = callbacks;
    
    // 生成唯一标识符用于调试
    const clientId = Math.random().toString(36).substr(2, 9);
    console.log(`🔧 [MJPEGStreamClient-${clientId}] 构造函数被调用，autoStart=${this.options.autoStart}, url=${this.options.streamUrl}`);
    
    // 如果启用自动启动，则异步启动连接
    if (this.options.autoStart) {
      console.log(`🚀 [MJPEGStreamClient-${clientId}] 自动启动连接`);
      setTimeout(() => this.start(), 0);
    }
  }

  /**
   * 获取当前连接状态
   */
  get status(): StreamStatus {
    return this._status;
  }

  /**
   * 获取当前错误信息
   */
  get error(): string | null {
    return this._error;
  }

  /**
   * 获取当前帧计数
   */
  get currentFrameCount(): number {
    return this.frameCount;
  }

  /**
   * 调试日志输出
   * @param level 日志级别
   * @param args 日志参数
   */
  private debugLogger(level: LogLevel, ...args: unknown[]): void {
    if (this.options.debug) {
      console[level](...args);
    }
  }

  /**
   * 更新连接状态
   * @param status 新状态
   * @param errorMessage 错误信息（可选）
   */
  private updateStatus(status: StreamStatus, errorMessage?: string): void {
    const previousStatus = this._status;
    this._status = status;
    
    // 更新错误状态
    if (errorMessage) {
      this._error = errorMessage;
    } else if (status === StreamStatus.CONNECTED) {
      this._error = null; // 连接成功时清除错误
    }

    // 触发状态订阅回调
    this.notifyStatusSubscribers(status, errorMessage);

    // 触发相应的回调
    if (errorMessage && this.callbacks.onError) {
      this.callbacks.onError(new Error(errorMessage));
    } else if (status === StreamStatus.CONNECTED && previousStatus !== StreamStatus.CONNECTED && this.callbacks.onConnect) {
      this.callbacks.onConnect();
    } else if (status === StreamStatus.CONNECTING && previousStatus !== StreamStatus.CONNECTING && this.callbacks.onConnecting) {
      this.callbacks.onConnecting();
    } else if (status === StreamStatus.STANDBY && previousStatus !== StreamStatus.STANDBY && this.callbacks.onDisconnect) {
      this.callbacks.onDisconnect();
    }
  }

  /**
   * 检查是否为有效的 JPEG 数据
   * @param data 二进制数据
   * @returns 是否为有效的 JPEG
   */
  private isValidJpeg(data: Uint8Array | ArrayBuffer): boolean {
    if (!data || (data instanceof ArrayBuffer ? data.byteLength < 2 : data.length < 2)) {
      return false;
    }

    const view = data instanceof ArrayBuffer
      ? new DataView(data)
      : new DataView(data.buffer);

    // 检查开头标志 (SOI: 0xFF 0xD8)
    const hasValidStart = view.getUint8(0) === 0xff && view.getUint8(1) === 0xd8;

    // 检查结尾标志 (EOI: 0xFF 0xD9)
    const length = data instanceof ArrayBuffer ? data.byteLength : data.length;
    const hasValidEnd = length >= 2 &&
      view.getUint8(length - 2) === 0xff &&
      view.getUint8(length - 1) === 0xd9;

    return hasValidStart && hasValidEnd;
  }

  /**
   * 从 MJPEG 流缓冲区中提取帧
   * @param buffer 缓冲区数据
   * @returns 提取的帧和剩余缓冲区
   */
  private extractMjpegFrames(buffer: Uint8Array): {
    frames: FrameData<T>[];
    remainingBuffer: Uint8Array;
  } {
    // 使用实际解析的 boundary，如果没有则使用默认值
    const boundary = this.actualBoundary || this.options.boundary || '--boundary';
    const { contentTypeMarker } = this.options;
    const decoder = new TextDecoder('ascii');
    const bufferStr = decoder.decode(buffer);

    let currentPos = 0;
    const extractedFrames: FrameData<T>[] = [];
    let contentLength: number | null = null;

    while (true) {
      // 查找边界
      const boundaryPos = bufferStr.indexOf(boundary, currentPos);
      if (boundaryPos === -1) break;

      // 查找内容类型标记
      const contentTypePos = bufferStr.indexOf(contentTypeMarker, boundaryPos);
      if (contentTypePos === -1) {
        currentPos = boundaryPos + boundary.length;
        continue;
      }

      // 查找内容长度标记
      const contentLengthMarker = 'Content-Length:';
      const contentLengthPos = bufferStr.indexOf(contentLengthMarker, contentTypePos);

      // 查找内容开始标记（空行）
      const contentStartPos = bufferStr.indexOf('\r\n\r\n', contentTypePos);
      if (contentStartPos === -1) {
        currentPos = boundaryPos + boundary.length;
        continue;
      }

      // 提取头部信息用于解析元数据
      const headers = bufferStr.substring(boundaryPos, contentStartPos);
      
      // 使用自定义元数据提取器提取元数据
      const metadata = this.options.metadataExtractor(headers) as T;

      // 提取 Content-Length 值
      if (contentLengthPos > -1) {
        const contentLengthEndPos = bufferStr.indexOf('\r\n', contentLengthPos);
        if (contentLengthEndPos > -1) {
          const contentLengthValue = bufferStr
            .substring(contentLengthPos + contentLengthMarker.length, contentLengthEndPos)
            .trim();
          contentLength = parseInt(contentLengthValue, 10);
          if (isNaN(contentLength)) {
            this.debugLogger('warn', '解析 Content-Length 失败:', contentLengthValue);
            contentLength = null;
          }
        }
      }

      // 计算内容开始位置（跳过空行）
      const imageStartIndex = contentStartPos + 4;

      // 查找下一个边界
      const nextBoundaryPos = bufferStr.indexOf(boundary, imageStartIndex);
      if (nextBoundaryPos === -1) {
        // 内容不完整，等待更多数据
        currentPos = boundaryPos + boundary.length;
        break;
      }

      // 提取图像数据
      const imageData = buffer.slice(imageStartIndex, nextBoundaryPos - 2);

      // 添加到提取的帧列表
      const frameData: FrameData<T> = {
        frame: imageData,
        contentLength: contentLength ?? 0,
        metadata: metadata
      };
      extractedFrames.push(frameData);

      // 保存最后的元数据
      this.lastMetadata = metadata;
      
      // 触发元数据回调
      if (this.callbacks.onMetadata) {
        this.callbacks.onMetadata(metadata);
      }

      // 移动到下一个边界
      currentPos = nextBoundaryPos + boundary.length;
    }

    // 返回提取的帧和剩余的缓冲区
    return {
      frames: extractedFrames,
      remainingBuffer: currentPos > 0 ? buffer.slice(currentPos - boundary.length) : buffer
    };
  }

  /**
   * 处理单个图像帧
   * @param frameData 帧数据
   * @returns 是否成功处理
   */
  private processFrame(frameData: FrameData<T>): boolean {
    const { frame } = frameData;

    // 检查是否为有效的 JPEG
    if (this.isValidJpeg(frame)) {
      // this.debugLogger('log', `检测到有效的JPEG头部，数据大小: ${frame.length}`);

      // 增加帧计数
      this.frameCount++;
      
      // 更新最后帧时间戳
      this.lastFrameTime = Date.now();

      // 触发帧回调
      if (this.callbacks.onFrame) {
        this.callbacks.onFrame(frameData);
        return true;
      }
    } else {
      this.debugLogger('warn', '无效的JPEG数据');
      if (this.options.debug) {
        // 打印前几个字节进行调试
        let hexDump = '';
        for (let i = 0; i < Math.min(20, frame.length); i++) {
          hexDump += frame[i].toString(16).padStart(2, '0') + ' ';
        }
        this.debugLogger('log', '图像数据头部:', hexDump);
      }
    }
    return false;
  }

  /**
   * 启动连接监控
   */
  private startConnectionMonitor(): void {
    // 清除现有的监控定时器
    this.stopConnectionMonitor();
    
    this.connectionMonitorTimer = setInterval(() => {
      if (this.status !== StreamStatus.CONNECTED || !this.lastMetadata) {
        return;
      }
      
      const currentTime = Date.now();
      const timeSinceLastFrame = currentTime - this.lastFrameTime;
      
      // 获取元数据中的ping周期，如果没有则使用默认值
      const pingPeriod = (this.lastMetadata as { SonarParams?: { pingPeriod?: number } })?.SonarParams?.pingPeriod || 1000;
      const timeoutThreshold = pingPeriod * this.options.frameTimeoutMultiplier;
      
      if (timeSinceLastFrame > timeoutThreshold) {
        this.debugLogger('warn', `帧超时检测: 已经${timeSinceLastFrame}ms未收到新帧，超过阈值${timeoutThreshold}ms，开始重连`);
        this.handleConnectionTimeout();
      }
    }, this.options.connectionMonitorInterval);
  }
  
  /**
   * 停止连接监控
   */
  private stopConnectionMonitor(): void {
    if (this.connectionMonitorTimer) {
      clearInterval(this.connectionMonitorTimer);
      this.connectionMonitorTimer = null;
    }
  }
  
  /**
   * 处理连接超时
   */
  private handleConnectionTimeout(): void {
    this.debugLogger('log', '检测到连接超时，重置连接');
    // 短暂延迟后强制重新连接
    setTimeout(() => {
      this.start(true); // 使用forceRestart=true强制重连
    }, 100);
  }

  /**
   * 订阅状态变化
   * @param callback 状态变化回调
   * @returns 取消订阅的函数
   */
  public subscribe(callback: StatusSubscriber): () => void {
    this.statusSubscribers.push(callback);
    // 立即触发当前状态
    callback(this._status, this._error || undefined);
    
    // 返回取消订阅的函数
    return () => {
      const index = this.statusSubscribers.indexOf(callback);
      if (index > -1) {
        this.statusSubscribers.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有订阅者状态变化
   * @param status 新状态
   * @param error 错误信息
   */
  private notifyStatusSubscribers(status: StreamStatus, error?: string): void {
    this.statusSubscribers.forEach(subscriber => {
      try {
        subscriber(status, error);
      } catch (err) {
        this.debugLogger('error', '状态订阅回调错误:', err);
      }
    });
    
    // 触发通用状态变化回调
    if (this.callbacks.onStatusChange) {
      try {
        this.callbacks.onStatusChange(status, error);
      } catch (err) {
        this.debugLogger('error', '状态变化回调错误:', err);
      }
    }
  }

  /**
   * 更新配置选项
   * @param newOptions 新的配置选项
   */
  public updateOptions(newOptions: Partial<StreamOptions>): void {
    const oldUrl = this.options.streamUrl;
    this.options = {
      ...this.options,
      ...newOptions
    } as Required<StreamOptions>;
    
    // 如果流地址发生变化且当前正在连接，则重新连接
    if (newOptions.streamUrl && newOptions.streamUrl !== oldUrl && this.status === StreamStatus.CONNECTED) {
      this.debugLogger('log', '流地址发生变化，重新连接');
      this.restart();
    }
  }

  /**
   * 重启连接
   */
  public async restart(): Promise<void> {
    this.debugLogger('log', '重启连接');
    // 短暂延迟后强制重新连接
    setTimeout(() => {
      this.start(true); // 使用forceRestart=true强制重连
    }, 100);
  }

  /**
   * 处理MJPEG流
   * @param reader 流读取器
   */
  private async processStream(reader: ReadableStreamDefaultReader<Uint8Array>): Promise<void> {
    // 设置连接状态为已连接
    this.updateStatus(StreamStatus.CONNECTED);
    
    // 启动连接监控
    this.startConnectionMonitor();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          this.debugLogger('log', '流读取完成');
          break;
        }

        if (value) {
          // 将新数据追加到缓冲区
          const newBuffer = new Uint8Array(this.buffer.length + value.length);
          newBuffer.set(this.buffer);
          newBuffer.set(value, this.buffer.length);
          this.buffer = newBuffer;

          // 处理所有可以提取的帧
          const result = this.extractMjpegFrames(this.buffer);
          this.buffer = result.remainingBuffer;

          // 处理提取的帧
          let processedFrames = 0;
          for (const frameData of result.frames) {
            if (this.processFrame(frameData)) {
              processedFrames++;
            }
          }

          // 如果缓冲区过大但没有成功处理帧，丢弃部分数据防止内存泄漏
          if (processedFrames === 0 && this.buffer.length > 1024 * 1024) {
            this.debugLogger('warn', '缓冲区已达到1MB但未找到完整帧，丢弃部分数据');
            this.buffer = this.buffer.slice(this.buffer.length - 1024 * 512); // 保留后半部分
          }
        }
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      if (this.callbacks.onError) {
        if (error.name === 'AbortError' && this.status === StreamStatus.STANDBY) {
          this.debugLogger('log', 'Fetch 请求被手动中止');
        } else {
          this.debugLogger('error', '处理流错误:', error.message);
          // 自动重连逻辑 - 在 processStream 的错误处理中也尝试重连
          if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.debugLogger('log', `尝试重连 (processStream, 第 ${this.reconnectAttempts} 次)..., 延迟 ${this.options.reconnectDelay}ms`);
            this.updateStatus(StreamStatus.CONNECTING); // 更新状态为连接中
            this.reconnectTimer = setTimeout(() => {
              this.start(true); // 使用forceRestart=true强制重连
            }, this.options.reconnectDelay);
          } else {
            this.updateStatus(StreamStatus.STANDBY, `达到最大重连次数，停止重连. 最后错误: ${error.message}`);
          }
        }
      }
    } finally {
      // 停止连接监控
      this.stopConnectionMonitor();
      
      if (this.status === StreamStatus.CONNECTED) {
        this.updateStatus(StreamStatus.STANDBY);
      }
    }
  }

  /**
   * 从 Content-Type 头部解析 boundary
   * @param contentType Content-Type 头部值
   * @returns 解析的 boundary 或 null
   */
  private parseBoundaryFromContentType(contentType: string): string | null {
    try {
      // 匹配 boundary= 后的值，支持带引号和不带引号的格式
      const boundaryMatch = contentType.match(/boundary=([^;\s]+)/i);
      if (boundaryMatch) {
        let boundary = boundaryMatch[1].trim();
        // 移除可能的引号
        if ((boundary.startsWith('"') && boundary.endsWith('"')) ||
            (boundary.startsWith("'") && boundary.endsWith("'"))) {
          boundary = boundary.slice(1, -1);
        }
        // 确保 boundary 以 -- 开头
        if (!boundary.startsWith('--')) {
          boundary = '--' + boundary;
        }
        return boundary;
      }
    } catch (err) {
      this.debugLogger('warn', '解析 Content-Type 中的 boundary 失败:', err);
    }
    return null;
  }

  /**
   * 启动 MJPEG 流
   * @param forceRestart 是否强制重启（用于重连场景）
   * @returns 是否成功启动
   */
  public async start(forceRestart: boolean = false): Promise<boolean> {
    // 如果已经连接，直接返回成功
    if (this.status === StreamStatus.CONNECTED && !forceRestart) {
      console.log(`✅ [MJPEGStreamClient] 已经连接，无需重复启动: ${this.options.streamUrl}`);
      return true;
    }
    
    // 如果正在连接且不是强制重启，跳过启动
    if (this.status === StreamStatus.CONNECTING && !forceRestart) {
      console.log(`⚠️ [MJPEGStreamClient] 正在连接中，跳过启动: ${this.options.streamUrl}`);
      return false;
    }
    
    // 如果是强制重启或重连场景，先停止现有连接
    if (forceRestart && (this.status === StreamStatus.CONNECTED || this.status === StreamStatus.CONNECTING)) {
      console.log(`🔄 [MJPEGStreamClient] 强制重启，先停止现有连接: ${this.options.streamUrl}`);
      this.stop();
    }

    try {
      this.updateStatus(StreamStatus.CONNECTING);
      console.log(`🔌 [MJPEGStreamClient] 尝试连接MJPEG流: ${this.options.streamUrl}`);
      this.debugLogger('log', '尝试连接MJPEG流:', this.options.streamUrl);
      this.reconnectAttempts = 0; // 重置重连尝试计数器

      // 创建 AbortController 用于可取消的 fetch 请求
      this.abortController = new AbortController();
      const { signal } = this.abortController;

      // 获取 MJPEG 流
      const response = await fetch(this.options.streamUrl, {
        signal,
        headers: this.options.headers
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      // 从 Content-Type 头部解析 boundary
      const contentType = response.headers.get('Content-Type');
      if (contentType) {
        const parsedBoundary = this.parseBoundaryFromContentType(contentType);
        if (parsedBoundary) {
          this.actualBoundary = parsedBoundary;
          this.debugLogger('log', `从响应头解析到 boundary: ${parsedBoundary}`);
        } else {
          this.debugLogger('warn', `无法从 Content-Type 解析 boundary: ${contentType}`);
          // 如果用户没有提供 boundary，使用默认值
          if (!this.options.boundary) {
            this.actualBoundary = '--boundary';
            this.debugLogger('log', '使用默认 boundary: --boundary');
          }
        }
      } else {
        this.debugLogger('warn', '响应头中没有 Content-Type');
        // 如果用户没有提供 boundary，使用默认值
        if (!this.options.boundary) {
          this.actualBoundary = '--boundary';
          this.debugLogger('log', '使用默认 boundary: --boundary');
        }
      }

      console.log(`✅ [MJPEGStreamClient] 成功连接MJPEG流: ${this.options.streamUrl}`);
      this.debugLogger('log', '成功连接MJPEG流:', this.options.streamUrl);

      // 使用 ReadableStream API 处理流数据
      if (response.body) {
        const reader = response.body.getReader();
        await this.processStream(reader);
      } else {
        throw new Error('响应没有正文');
      }

      return true;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      this.debugLogger('error', 'MJPEG流错误:', error.message);
      
      // 检查是否已经被停止，如果是则不进行重连
      if (this.status === StreamStatus.STANDBY) {
        return false;
      }
      
      // 自动重连逻辑
      if (this.reconnectAttempts < this.options.maxReconnectAttempts) {
        this.reconnectAttempts++;
        this.debugLogger('log', `尝试重连 (第 ${this.reconnectAttempts} 次)..., 延迟 ${this.options.reconnectDelay}ms`);
        this.updateStatus(StreamStatus.CONNECTING); // 更新状态为连接中
        this.reconnectTimer = setTimeout(() => {
          this.start(true); // 使用forceRestart=true强制重连
        }, this.options.reconnectDelay);
      } else {
        this.updateStatus(StreamStatus.STANDBY, `达到最大重连次数，停止重连. 最后错误: ${error.message}`);
      }
      return false;
    }
  }

  /**
   * 停止 MJPEG 流
   */
  public stop(): void {
    console.log(`🛑 [MJPEGStreamClient] 停止MJPEG流: ${this.options.streamUrl}`);
    
    // 标记为正在停止，避免重复调用
    if (this.status === StreamStatus.STANDBY) {
      this.debugLogger('log', 'MJPEG流已经是停止状态');
      return;
    }
    
    // 立即更新状态为停止中
    this.updateStatus(StreamStatus.STANDBY);
    
    // 中止正在进行的请求
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }

    // 清理重连定时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // 停止连接监控
    this.stopConnectionMonitor();

    this.debugLogger('log', 'MJPEG流已停止');
  }

  /**
   * 切换流的状态（开始/停止）
   * @returns 如果开始流，则返回开始是否成功；如果停止流，则返回 undefined
   */
  public async toggle(): Promise<boolean | undefined> {
    if (this.status === StreamStatus.STANDBY) {
      return await this.start();
    } else if (this.status === StreamStatus.CONNECTED || this.status === StreamStatus.CONNECTING) {
      this.stop();
      return undefined;
    }
    return false;
  }
} 