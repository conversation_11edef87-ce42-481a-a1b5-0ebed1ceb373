/**
 * MJPEG流处理库的类型定义
 */

// 定义元数据接口，允许扩展
export interface StreamMetadata {
  [key: string]: unknown;
}

// 声纳特定的元数据，作为示例扩展
export interface SonarMetadata extends StreamMetadata {
  SonarParams?: {
    pingPeriod?: number;
    beamNum?: number;
    velocity?: number;
    gain?: number;
    gamma?: number;
    mode?: number;
    higherFreq?: boolean;
    timestamp?: number;
    range?: number;
  };
}

// 定义帧数据结构
export interface FrameData<T extends StreamMetadata = StreamMetadata> {
  frame: Uint8Array;
  contentLength: number;
  metadata: T;
}

// 流配置选项
export interface StreamOptions {
  // 流地址
  streamUrl: string;
  // 边界字符串
  boundary?: string;
  // 内容类型标记
  contentTypeMarker?: string;
  // 是否启用调试
  debug?: boolean;
  // 自定义请求头
  headers?: Record<string, string>;
  // 自定义元数据提取器（从头部信息中提取元数据）
  metadataExtractor?: (headers: string) => StreamMetadata;
  // 重连延迟时间
  reconnectDelay?: number;
  // 最大重连尝试次数
  maxReconnectAttempts?: number;
  // 是否自动启动连接
  autoStart?: boolean;
  // 帧超时倍数（基于ping周期）
  frameTimeoutMultiplier?: number;
  // 连接监控间隔（毫秒）
  connectionMonitorInterval?: number;
}

// 状态订阅回调类型
export type StatusSubscriber = (status: StreamStatus, error?: string) => void;

// 事件处理回调
export interface StreamCallbacks<T extends StreamMetadata = StreamMetadata> {
  // 连接成功回调
  onConnect?: () => void;
  // 断开连接回调
  onDisconnect?: () => void;
  // 正在连接回调
  onConnecting?: () => void;
  // 错误处理回调
  onError?: (error: Error) => void;
  // 接收到新帧回调
  onFrame?: (frameData: FrameData<T>) => void;
  // 接收到元数据回调
  onMetadata?: (metadata: T) => void;
  // 状态变化回调
  onStatusChange?: StatusSubscriber;
}

// 流状态枚举
export enum StreamStatus {
  STANDBY = 'standby',
  CONNECTING = 'connecting',
  CONNECTED = 'connected'
}

// 日志级别类型
export type LogLevel = 'log' | 'warn' | 'error'; 