import { SonarMetadata } from './types';

/**
 * 提取声纳设备特定的元数据
 * 从 MJPEG 流头部信息中提取 SonarParams
 * @param headers 流头部信息
 * @returns 提取的声纳元数据
 */
export function extractSonarMetadata(headers: string): SonarMetadata {
  const sonarParamsMarker = 'SonarParams:';
  const sonarParamsPos = headers.indexOf(sonarParamsMarker);
  
  if (sonarParamsPos > -1) {
    // 尝试多种行结束符
    let sonarParamsEndPos = -1;
    const possibleLineEndings = ['\r\n', '\n', '\r'];
    
    for (const ending of possibleLineEndings) {
      const endPos = headers.indexOf(ending, sonarParamsPos);
      if (endPos > -1) {
        sonarParamsEndPos = endPos;
        break;
      }
    }
    
    // 如果找不到行结束符，则尝试使用整个剩余字符串
    if (sonarParamsEndPos === -1) {
      sonarParamsEndPos = headers.length;
    }
    
    // 提取 SonarParams 值
    const sonarParamsValue = headers
      .substring(sonarParamsPos + sonarParamsMarker.length, sonarParamsEndPos)
      .trim();

    try {
      const sonarParamsObj = JSON.parse(sonarParamsValue);
      // 添加当前时间戳
      sonarParamsObj.timestamp = Date.now();
      return { SonarParams: sonarParamsObj };
    } catch (error) {
      console.warn('解析 SonarParams JSON 失败:', error, "原始值:", sonarParamsValue);
    }
  }
  
  return {};
}

/**
 * 创建图像 URL
 * 将二进制图像数据转换为 URL
 * @param imageData 图像二进制数据
 * @param mimeType 图像 MIME 类型
 * @returns 创建的图像 URL
 */
export function createImageUrl(imageData: Uint8Array, mimeType: string = 'image/jpeg'): string {
  const blob = new Blob([imageData], { type: mimeType });
  return URL.createObjectURL(blob);
}

/**
 * 释放图像 URL
 * @param url 要释放的 URL
 * @param delay 延迟释放的毫秒数
 */
export function revokeImageUrl(url: string, delay: number = 100): void {
  setTimeout(() => URL.revokeObjectURL(url), delay);
}

/**
 * 格式化时间戳
 * @param date 日期对象（默认为当前时间）
 * @returns 格式化的时间戳字符串
 */
export function formatTimestamp(date: Date = new Date()): string {
  return date.toISOString().replace('T', ' ').substring(0, 19);
}