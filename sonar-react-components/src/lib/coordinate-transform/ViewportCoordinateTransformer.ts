/**
 * 视口坐标转换器
 * 处理缩放变换下的坐标系转换问题
 */

import { PixelCoordinate } from '../sonar-geometry/types';

export interface TransformConfig {
  scale: number;
  transformOrigin?: string; // 可选，目前简化版本不需要
}

/**
 * 视口坐标转换器类
 * 负责在视口坐标系和图像坐标系之间进行转换
 */
export class ViewportCoordinateTransformer {
  private scale: number;
  private transformOrigin: string;

  constructor(config: TransformConfig) {
    this.scale = config.scale;
    this.transformOrigin = config.transformOrigin || 'center center';
  }

  /**
   * 视口坐标转换为图像坐标
   * @param viewportCoord 视口坐标（相对于缩放后的元素边界）
   * @returns 图像坐标（原始图像坐标系）
   */
  viewportToImage(viewportCoord: PixelCoordinate): PixelCoordinate {
    // 如果缩放比例为1，直接返回原坐标
    if (this.scale === 1) {
      return { ...viewportCoord };
    }

    // 简化的坐标转换逻辑
    // 由于 getBoundingClientRect() 返回的是缩放后的边界，
    // 而 viewportCoord 是相对于这个缩放后边界的坐标，
    // 我们只需要简单地除以缩放比例即可得到原始图像坐标
    return {
      x: viewportCoord.x / this.scale,
      y: viewportCoord.y / this.scale,
    };
  }

  /**
   * 图像坐标转换为视口坐标
   * @param imageCoord 图像坐标（原始图像坐标系）
   * @returns 视口坐标（缩放后的坐标系）
   */
  imageToViewport(imageCoord: PixelCoordinate): PixelCoordinate {
    // 如果缩放比例为1，直接返回原坐标
    if (this.scale === 1) {
      return { ...imageCoord };
    }

    // 简化的坐标转换逻辑
    // 将原始图像坐标乘以缩放比例即可得到视口坐标
    return {
      x: imageCoord.x * this.scale,
      y: imageCoord.y * this.scale,
    };
  }

  /**
   * 获取当前缩放比例
   */
  getScale(): number {
    return this.scale;
  }

  /**
   * 获取当前变换原点
   */
  getTransformOrigin(): string {
    return this.transformOrigin;
  }

  /**
   * 更新转换器配置
   */
  updateConfig(config: Partial<TransformConfig>): void {
    if (config.scale !== undefined) {
      this.scale = config.scale;
    }
    if (config.transformOrigin !== undefined) {
      this.transformOrigin = config.transformOrigin;
    }
  }

  /**
   * 检查转换器是否需要进行坐标转换
   */
  needsTransform(): boolean {
    return this.scale !== 1;
  }
}

/**
 * 创建坐标转换器的工厂函数
 */
export function createViewportTransformer(config: TransformConfig): ViewportCoordinateTransformer {
  return new ViewportCoordinateTransformer(config);
}
