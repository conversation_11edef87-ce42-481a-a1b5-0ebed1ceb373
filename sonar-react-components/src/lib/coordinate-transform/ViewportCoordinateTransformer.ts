/**
 * 视口坐标转换器
 * 处理缩放变换下的坐标系转换问题
 */

import { PixelCoordinate } from '../sonar-geometry/types';

export interface TransformConfig {
  scale: number;
  transformOrigin: string;
  containerWidth: number;
  containerHeight: number;
}

/**
 * 视口坐标转换器类
 * 负责在视口坐标系和图像坐标系之间进行转换
 */
export class ViewportCoordinateTransformer {
  private scale: number;
  private transformOrigin: string;
  private containerWidth: number;
  private containerHeight: number;

  constructor(config: TransformConfig) {
    this.scale = config.scale;
    this.transformOrigin = config.transformOrigin;
    this.containerWidth = config.containerWidth;
    this.containerHeight = config.containerHeight;
  }

  /**
   * 视口坐标转换为图像坐标
   * @param viewportCoord 视口坐标（缩放后的坐标系）
   * @returns 图像坐标（原始图像坐标系）
   */
  viewportToImage(viewportCoord: PixelCoordinate): PixelCoordinate {
    // 如果缩放比例为1，直接返回原坐标
    if (this.scale === 1) {
      return { ...viewportCoord };
    }

    // 处理 transform-origin: center center 的情况
    if (this.transformOrigin === 'center center') {
      // 计算缩放中心点
      const centerX = this.containerWidth / 2;
      const centerY = this.containerHeight / 2;

      // 将视口坐标转换为相对于缩放中心的坐标
      const relativeX = viewportCoord.x - centerX;
      const relativeY = viewportCoord.y - centerY;

      // 应用逆缩放变换
      const scaledRelativeX = relativeX / this.scale;
      const scaledRelativeY = relativeY / this.scale;

      // 转换回绝对坐标
      return {
        x: scaledRelativeX + centerX,
        y: scaledRelativeY + centerY,
      };
    }

    // 其他 transform-origin 情况（如需要可以扩展）
    // 目前只处理 center center，这是最常见的情况
    return {
      x: viewportCoord.x / this.scale,
      y: viewportCoord.y / this.scale,
    };
  }

  /**
   * 图像坐标转换为视口坐标
   * @param imageCoord 图像坐标（原始图像坐标系）
   * @returns 视口坐标（缩放后的坐标系）
   */
  imageToViewport(imageCoord: PixelCoordinate): PixelCoordinate {
    // 如果缩放比例为1，直接返回原坐标
    if (this.scale === 1) {
      return { ...imageCoord };
    }

    // 处理 transform-origin: center center 的情况
    if (this.transformOrigin === 'center center') {
      // 计算缩放中心点
      const centerX = this.containerWidth / 2;
      const centerY = this.containerHeight / 2;

      // 将图像坐标转换为相对于缩放中心的坐标
      const relativeX = imageCoord.x - centerX;
      const relativeY = imageCoord.y - centerY;

      // 应用缩放变换
      const scaledRelativeX = relativeX * this.scale;
      const scaledRelativeY = relativeY * this.scale;

      // 转换回绝对坐标
      return {
        x: scaledRelativeX + centerX,
        y: scaledRelativeY + centerY,
      };
    }

    // 其他 transform-origin 情况
    return {
      x: imageCoord.x * this.scale,
      y: imageCoord.y * this.scale,
    };
  }

  /**
   * 获取当前缩放比例
   */
  getScale(): number {
    return this.scale;
  }

  /**
   * 获取当前变换原点
   */
  getTransformOrigin(): string {
    return this.transformOrigin;
  }

  /**
   * 更新转换器配置
   */
  updateConfig(config: Partial<TransformConfig>): void {
    if (config.scale !== undefined) {
      this.scale = config.scale;
    }
    if (config.transformOrigin !== undefined) {
      this.transformOrigin = config.transformOrigin;
    }
    if (config.containerWidth !== undefined) {
      this.containerWidth = config.containerWidth;
    }
    if (config.containerHeight !== undefined) {
      this.containerHeight = config.containerHeight;
    }
  }

  /**
   * 检查转换器是否需要进行坐标转换
   */
  needsTransform(): boolean {
    return this.scale !== 1;
  }
}

/**
 * 创建坐标转换器的工厂函数
 */
export function createViewportTransformer(config: TransformConfig): ViewportCoordinateTransformer {
  return new ViewportCoordinateTransformer(config);
}
