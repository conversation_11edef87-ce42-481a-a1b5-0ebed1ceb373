{"name": "sonar-react-components", "version": "1.0.0", "description": "React components for Sonar device interaction", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc && vite build", "dev": "vite build --watch", "lint": "eslint src --ext ts,tsx", "typecheck": "tsc --noEmit"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"axios": "^1.7.9"}, "devDependencies": {"@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "typescript": "~5.7.2", "vite": "^6.1.0"}, "keywords": ["react", "sonar", "components", "marine", "mjpeg"], "author": "", "license": "MIT"}