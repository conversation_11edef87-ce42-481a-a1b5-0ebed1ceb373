<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瀚界 C900 SE2 - 实时日志监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e1e 0%, #262626 100%);
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #111827;
            border-bottom: 1px solid #374151;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo-container {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(8px);
            border-radius: 8px;
            padding: 6px;
            border: 2px solid rgba(59, 130, 246, 0.4);
            box-shadow: 0 0 14px rgba(59, 130, 246, 0.5);
        }

        .logo {
            width: 28px;
            height: 28px;
            filter: brightness(2.5) contrast(1.3) drop-shadow(0 0 4px rgba(59, 130, 246, 0.8));
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
        }

        .subtitle {
            color: #60A5FA;
            font-size: 16px;
            margin-left: 12px;
            font-weight: normal;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .status-card {
            background: #1F2937;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 8px 12px;
            min-width: 100px;
            text-align: center;
        }

        .status-label {
            font-size: 11px;
            color: #9CA3AF;
            margin-bottom: 2px;
        }

        .status-value {
            font-size: 14px;
            font-weight: bold;
        }

        .status-connected {
            color: #10B981;
        }

        .status-disconnected {
            color: #EF4444;
        }

        .status-connecting {
            color: #F59E0B;
        }

        .btn {
            background: #374151;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            background: #4B5563;
        }

        .btn-primary {
            background: #3B82F6;
        }

        .btn-primary:hover {
            background: #2563EB;
        }

        .btn-danger {
            background: #EF4444;
        }

        .btn-danger:hover {
            background: #DC2626;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 24px;
            min-height: 0;
            overflow: hidden;
        }

        .controls-section {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }


        .log-panel {
            flex: 1;
            background: #1F2937;
            border: 1px solid #374151;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .log-header {
            background: #111827;
            padding: 16px 20px;
            border-bottom: 1px solid #374151;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .log-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .log-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.5;
            background: #0F172A;
            min-height: 0;
            height: 0;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 4px 0;
            border-bottom: 1px solid rgba(55, 65, 81, 0.3);
            display: grid;
            grid-template-columns: auto 1fr;
            align-items: flex-start;
            gap: 12px;
        }

        .log-content.show-receive-time .log-entry {
            grid-template-columns: 80px auto 1fr;
        }

        .log-timestamp {
            color: #6B7280;
            font-size: 12px;
        }

        .log-receive-time {
            color: #4B5563;
            font-size: 11px;
            text-align: right;
            padding-right: 8px;
            display: none;
        }

        .log-content.show-receive-time .log-receive-time {
            display: block;
        }

        .log-message {
            color: #E5E7EB;
            flex: 1;
            word-break: break-word;
        }

        .connection-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .indicator-connected {
            background: #10B981;
            box-shadow: 0 0 4px #10B981;
        }

        .indicator-disconnected {
            background: #EF4444;
        }

        .indicator-connecting {
            background: #F59E0B;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #6B7280;
            text-align: center;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        /* 滚动条样式 */
        .log-content::-webkit-scrollbar {
            width: 8px;
        }

        .log-content::-webkit-scrollbar-track {
            background: #111827;
        }

        .log-content::-webkit-scrollbar-thumb {
            background: #374151;
            border-radius: 4px;
        }

        .log-content::-webkit-scrollbar-thumb:hover {
            background: #4B5563;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo-container">
                <img src="/hj-logo.png" alt="瀚界 Logo" class="logo">
            </div>
            <div>
                <span class="title">瀚界 C900 SE2</span>
                <span class="subtitle">实时日志监控</span>
            </div>
        </div>
        <div class="header-right">
            <div class="status-card">
                <div class="status-label">日志条数</div>
                <div class="status-value" id="logCount">0</div>
            </div>
            <div class="status-card">
                <div class="status-label">重连次数</div>
                <div class="status-value" id="reconnectCount">0</div>
            </div>
            <div class="status-card">
                <div class="status-label">连接ID</div>
                <div class="status-value" id="connectionId">-</div>
            </div>
            <div class="status-card">
                <div class="status-label">运行时间</div>
                <div class="status-value" id="uptime">00:00:00</div>
            </div>
            <div class="status-card">
                <div class="status-label">连接状态</div>
                <div class="status-value" id="connectionStatus">
                    <span class="connection-indicator indicator-disconnected"></span>
                    <span>未连接</span>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="controls-section">
            <button class="btn btn-primary" id="connectBtn" onclick="toggleConnection()">
                连接日志流
            </button>
            <button class="btn" onclick="clearLogs()">
                清空日志
            </button>
            <button class="btn" onclick="exportLogs()">
                导出日志
            </button>
            <button class="btn btn-danger" onclick="forceDisconnect()">
                强制断开
            </button>
        </div>

        <div class="log-panel">
            <div class="log-header">
                <h3 class="log-title">声呐系统实时日志</h3>
                <div class="log-controls">
                    <label>
                        <input type="checkbox" id="showReceiveTime"> 显示接收时间
                    </label>
                    <label>
                        <input type="checkbox" id="autoScroll" checked> 自动滚动
                    </label>
                </div>
            </div>
            <div class="log-content" id="logContent">
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <div>暂无日志数据</div>
                    <div style="font-size: 12px; margin-top: 8px;">点击"连接日志流"开始接收实时日志</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SonarLogStream {
            constructor(config = {}) {
                this.baseUrl = config.baseUrl || `${window.location.protocol}//${window.location.hostname}:${window.location.port || (window.location.protocol === 'https:' ? '443' : '80')}`;
                this.maxLogLines = config.maxLogLines || 1000;
                this.onLogReceived = config.onLogReceived || this.defaultLogHandler;
                this.onError = config.onError || this.defaultErrorHandler;
                this.onReconnect = config.onReconnect || this.defaultReconnectHandler;
                this.onConnected = config.onConnected || (() => {});
                this.onDisconnected = config.onDisconnected || (() => {});
                
                this.eventSource = null;
                this.isConnected = false;
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 3;
                this.logBuffer = [];
                this.connectionId = this.generateConnectionId();
                this.lastEventId = 0;
                this.startTime = null;
                
                this.reconnectTimeout = null;
                this.connectionTimeout = null;
            }

            generateConnectionId() {
                return Math.random().toString(36).substr(2, 9);
            }

            start() {
                if (this.isConnected || this.isConnecting) {
                    console.warn(`[SonarLogStream-${this.connectionId}] 连接已存在，跳过启动`);
                    return;
                }

                this.isConnecting = true;
                this.startTime = new Date();
                console.log(`[SonarLogStream-${this.connectionId}] 启动实时日志流...`);
                
                try {
                    if (this.eventSource) {
                        this.eventSource.close();
                    }

                    this.connectionTimeout = setTimeout(() => {
                        if (this.isConnecting && !this.isConnected) {
                            console.warn(`[SonarLogStream-${this.connectionId}] 连接超时`);
                            this.handleConnectionError('连接超时');
                        }
                    }, 10000);

                    this.eventSource = new EventSource(`${this.baseUrl}/api/v1/log/stream`);
                    
                    this.eventSource.onopen = (event) => {
                        console.log(`[SonarLogStream-${this.connectionId}] SSE 连接已建立`);
                        this.isConnected = true;
                        this.isConnecting = false;
                        this.reconnectAttempts = 0;
                        
                        if (this.connectionTimeout) {
                            clearTimeout(this.connectionTimeout);
                            this.connectionTimeout = null;
                        }
                        
                        this.onConnected();
                    };

                    this.eventSource.addEventListener('log', (event) => {
                        try {
                            this.lastEventId = parseInt(event.lastEventId) || this.lastEventId + 1;
                            const logData = JSON.parse(event.data);
                            this.handleLogMessage(logData);
                        } catch (e) {
                            console.error(`[SonarLogStream-${this.connectionId}] 解析日志数据失败:`, e);
                        }
                    });

                    this.eventSource.onerror = (event) => {
                        console.error(`[SonarLogStream-${this.connectionId}] SSE 连接错误:`, event);
                        this.handleConnectionError('SSE连接错误');
                    };

                } catch (error) {
                    console.error(`[SonarLogStream-${this.connectionId}] 启动失败:`, error);
                    this.handleConnectionError(`启动失败: ${error.message}`);
                }
            }

            handleConnectionError(reason) {
                this.isConnected = false;
                this.isConnecting = false;
                
                if (this.connectionTimeout) {
                    clearTimeout(this.connectionTimeout);
                    this.connectionTimeout = null;
                }
                
                this.onError(reason);
                this.onDisconnected();
                
                if (this.reconnectAttempts < this.maxReconnectAttempts && 
                    !reason.includes('手动断开')) {
                    
                    this.reconnectAttempts++;
                    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);
                    console.log(`[SonarLogStream-${this.connectionId}] ${delay}ms 后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    
                    if (this.reconnectTimeout) {
                        clearTimeout(this.reconnectTimeout);
                    }
                    
                    this.reconnectTimeout = setTimeout(() => {
                        if (!this.isConnected && !this.isConnecting) {
                            this.reconnect();
                        }
                    }, delay);
                }
            }

            stop(manual = true) {
                console.log(`[SonarLogStream-${this.connectionId}] 停止实时日志流... (手动: ${manual})`);
                
                this.isConnected = false;
                this.isConnecting = false;
                
                if (this.connectionTimeout) {
                    clearTimeout(this.connectionTimeout);
                    this.connectionTimeout = null;
                }
                if (this.reconnectTimeout) {
                    clearTimeout(this.reconnectTimeout);
                    this.reconnectTimeout = null;
                }
                
                if (this.eventSource) {
                    this.eventSource.close();
                    this.eventSource = null;
                }
                
                if (manual) {
                    this.reconnectAttempts = this.maxReconnectAttempts;
                }
                
                this.onDisconnected();
            }

            reconnect() {
                console.log(`[SonarLogStream-${this.connectionId}] 尝试重新连接...`);
                this.onReconnect(this.reconnectAttempts);
                
                this.stop(false);
                
                setTimeout(() => {
                    this.start();
                }, 1000);
            }

            handleLogMessage(logData) {
                this.logBuffer.push({
                    ...logData,
                    id: Date.now() + Math.random(),
                    receivedAt: new Date().toISOString()
                });

                if (this.logBuffer.length > this.maxLogLines) {
                    this.logBuffer = this.logBuffer.slice(-this.maxLogLines);
                }

                this.onLogReceived(logData, this.logBuffer);
            }

            getConnectionStatus() {
                return {
                    connected: this.isConnected,
                    connecting: this.isConnecting,
                    reconnectAttempts: this.reconnectAttempts,
                    logCount: this.logBuffer.length,
                    connectionId: this.connectionId,
                    uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0
                };
            }

            clearLogs() {
                this.logBuffer = [];
            }

            defaultLogHandler(logData) {
                console.log(`[SonarLogStream-${this.connectionId}] 收到日志:`, logData.message);
            }

            defaultErrorHandler(error) {
                console.error(`[SonarLogStream-${this.connectionId}] 错误:`, error);
            }

            defaultReconnectHandler(attempts) {
                console.log(`[SonarLogStream-${this.connectionId}] 重连尝试 ${attempts}`);
            }
        }

        // 全局变量
        let logStream = null;
        let stats = {
            logCount: 0,
            reconnectCount: 0,
            lastLogTime: null
        };

        // 工具函数：处理日志消息格式
        function processLogMessage(message) {
            let processedTimestamp = null;
            let processedMessage = message;
            
            // 正则表达式匹配时间戳和要移除的部分
            const logPattern = /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{4})\s+\d+\s+sonar_service\.sh\[\d+\]:\s*(.*)$/;
            const match = message.match(logPattern);
            
            if (match) {
                const originalTimestamp = match[1];
                const logContent = match[2];
                
                // 解析原始时间戳并转换为本地时区
                try {
                    const date = new Date(originalTimestamp);
                    // 格式化为本地时区，保持时区标志
                    processedTimestamp = date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit', 
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        timeZoneName: 'short'
                    }).replace(/\//g, '-');
                    
                    processedMessage = logContent;
                } catch (e) {
                    // 如果时间解析失败，保持原始消息
                    console.warn('时间戳解析失败:', originalTimestamp, e);
                    processedMessage = logContent;
                }
            } else {
                // 如果格式不匹配，尝试只移除 sonar_service.sh[xxx] 部分
                processedMessage = message.replace(/\s+\d+\s+sonar_service\.sh\[\d+\]:\s*/, ' ');
            }
            
            return { timestamp: processedTimestamp, message: processedMessage };
        }

        // 日志处理函数
        function onLogReceived(logData) {
            stats.logCount++;
            stats.lastLogTime = new Date();
            
            // 处理日志消息格式
            const processed = processLogMessage(logData.message);
            
            // 使用处理后的时间戳或当前时间
            const displayTimestamp = processed.timestamp || new Date().toLocaleTimeString();
            const receiveTime = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-receive-time">(${receiveTime})</span>
                <span class="log-timestamp">[${displayTimestamp}]</span>
                <span class="log-message">${escapeHtml(processed.message)}</span>
            `;
            
            const logContainer = document.getElementById('logContent');
            
            // 移除空状态
            const emptyState = logContainer.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }
            
            logContainer.appendChild(logEntry);
            
            // 自动滚动
            if (document.getElementById('autoScroll').checked) {
                requestAnimationFrame(() => {
                    logContainer.scrollTop = logContainer.scrollHeight;
                });
            }
            
            // 限制日志条数
            const maxEntries = 500;
            while (logContainer.children.length > maxEntries) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        function onConnected() {
            updateConnectionStatus('已连接', 'connected');
            updateConnectButton(true);
            console.log('SSE连接建立成功');
        }

        function onDisconnected() {
            updateConnectButton(false);
            console.log('SSE连接已断开');
        }

        function onError(error) {
            updateConnectionStatus(`连接错误: ${error}`, 'disconnected');
            console.error(`连接错误: ${error}`);
        }

        function onReconnect(attempts) {
            stats.reconnectCount = attempts;
            updateConnectionStatus(`重连中 (${attempts})`, 'connecting');
            console.log(`重连尝试: ${attempts}`);
        }

        function updateConnectionStatus(text, status) {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.connection-indicator');
            const textSpan = statusElement.querySelector('span:last-child');
            
            textSpan.textContent = text;
            
            indicator.className = 'connection-indicator';
            switch (status) {
                case 'connected':
                    indicator.classList.add('indicator-connected');
                    break;
                case 'connecting':
                    indicator.classList.add('indicator-connecting');
                    break;
                default:
                    indicator.classList.add('indicator-disconnected');
            }
        }

        function updateConnectButton(connected) {
            const btn = document.getElementById('connectBtn');
            btn.textContent = connected ? '断开日志流' : '连接日志流';
            btn.disabled = false;
        }

        function clearLogs() {
            const logContainer = document.getElementById('logContent');
            logContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <div>暂无日志数据</div>
                    <div style="font-size: 12px; margin-top: 8px;">点击"连接日志流"开始接收实时日志</div>
                </div>
            `;
            if (logStream) {
                logStream.clearLogs();
            }
            stats.logCount = 0;
        }

        function exportLogs() {
            if (!logStream || logStream.logBuffer.length === 0) {
                alert('暂无日志数据可导出');
                return;
            }

            const logs = logStream.logBuffer.map(log => {
                const processed = processLogMessage(log.message);
                const displayTimestamp = processed.timestamp || new Date(log.receivedAt).toLocaleString();
                return `[${displayTimestamp}] ${processed.message}`;
            }).join('\n');

            const blob = new Blob([logs], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sonar-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // 连接控制
        function toggleConnection() {
            const btn = document.getElementById('connectBtn');
            btn.disabled = true;
            
            if (logStream && (logStream.isConnected || logStream.isConnecting)) {
                logStream.stop(true);
                updateConnectionStatus('已断开', 'disconnected');
            } else {
                startLogStream();
            }
        }

        function startLogStream() {
            console.log('启动日志流...');
            
            if (logStream) {
                logStream.stop(true);
            }
            
            logStream = new SonarLogStream({
                // baseUrl will use current page's hostname automatically
                onLogReceived: onLogReceived,
                onError: onError,
                onReconnect: onReconnect,
                onConnected: onConnected,
                onDisconnected: onDisconnected
            });
            
            logStream.start();
            updateConnectionStatus('连接中...', 'connecting');
        }

        function forceDisconnect() {
            console.log('强制断开所有连接...');
            if (logStream) {
                logStream.stop(true);
                updateConnectionStatus('已断开', 'disconnected');
            }
        }

        // 统计信息更新
        function updateStats() {
            document.getElementById('logCount').textContent = stats.logCount;
            document.getElementById('reconnectCount').textContent = stats.reconnectCount;
            
            if (logStream) {
                const status = logStream.getConnectionStatus();
                document.getElementById('connectionId').textContent = status.connectionId;
                document.getElementById('uptime').textContent = formatUptime(status.uptime);
            }
        }

        // 控制接收时间显示
        function toggleReceiveTime() {
            const showReceiveTime = document.getElementById('showReceiveTime').checked;
            const logContent = document.getElementById('logContent');
            
            if (showReceiveTime) {
                logContent.classList.add('show-receive-time');
            } else {
                logContent.classList.remove('show-receive-time');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('实时日志页面加载完成');
            
            // 绑定接收时间显示控制事件
            document.getElementById('showReceiveTime').addEventListener('change', toggleReceiveTime);
            
            // 每秒更新统计信息
            setInterval(updateStats, 1000);
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            console.log('页面卸载，断开连接...');
            if (logStream) {
                logStream.stop(true);
            }
        });
    </script>
</body>
</html>