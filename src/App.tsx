import "./App.css";
import SonarControl from "../sonar-react-components/src/components/SonarControl";
import SonarImage from "../sonar-react-components/src/components/SonarImage";
import SonarRangeVertical, { SonarRangeVerticalRef } from "../sonar-react-components/src/components/SonarRangeVertical";
import TopBrandBar from "./components/TopBrandBar";
import { useState, useCallback, useEffect, useRef } from "react";

function App() {
  const sonarIP = import.meta.env.VITE_SONAR_IP || window.location.hostname;
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // 悬浮状态管理
  const [showTopBar, setShowTopBar] = useState(false);
  const [showLeftPanel, setShowLeftPanel] = useState(false);
  
  // 声呐量程控制
  const sonarRangeRef = useRef<SonarRangeVerticalRef>(null);
  const [currentSonarMode, setCurrentSonarMode] = useState<number>(7); // 默认模式7(1米)
  
  // 延迟隐藏定时器
  const [hideTopBarTimer, setHideTopBarTimer] = useState<NodeJS.Timeout | null>(null);
  const [hideLeftPanelTimer, setHideLeftPanelTimer] = useState<NodeJS.Timeout | null>(null);
  
  // 使用 useCallback 稳定回调函数引用
  const handleConnect = useCallback(() => console.log('已连接到声纳'), []);
  const handleDisconnect = useCallback(() => console.log('已断开声纳连接'), []);
  const handleError = useCallback((error: Error) => console.error('声纳连接错误:', error), []);
  
  // 处理声呐模式变化
  const handleSonarModeChange = useCallback((mode: number) => {
    setCurrentSonarMode(mode);
  }, []);

  // 鼠标边缘检测
  useEffect(() => {
    if (!isFullscreen) return;

    const handleMouseMove = (e: MouseEvent) => {
      const EDGE_THRESHOLD = 60; // 边缘检测阈值（像素）- 增加到60px提升易用性
      const HIDE_DELAY = 300; // 隐藏延迟（毫秒）
      
      // 检测顶部边缘
      const isNearTop = e.clientY < EDGE_THRESHOLD;
      
      // 检测鼠标是否在顶部悬浮栏内（通过DOM元素检测）
      let isInTopBar = false;
      if (showTopBar) {
        const topBarElement = document.querySelector('.fixed.top-0.left-0.right-0.z-60');
        if (topBarElement) {
          const rect = topBarElement.getBoundingClientRect();
          isInTopBar = e.clientX >= rect.left && e.clientX <= rect.right && 
                       e.clientY >= rect.top && e.clientY <= rect.bottom;
        }
      }
      
      // 顶部栏显示/隐藏逻辑
      const shouldShowTopBar = isNearTop || isInTopBar;
      if (shouldShowTopBar) {
        setShowTopBar(true);
        if (hideTopBarTimer) {
          clearTimeout(hideTopBarTimer);
          setHideTopBarTimer(null);
        }
      } else if (showTopBar && !hideTopBarTimer) {
        const timer = setTimeout(() => {
          setShowTopBar(false);
          setHideTopBarTimer(null);
        }, HIDE_DELAY);
        setHideTopBarTimer(timer);
      }
      
      // 检测左侧边缘
      const isNearLeft = e.clientX < EDGE_THRESHOLD;
      
      // 检测鼠标是否在左侧悬浮面板内（通过DOM元素检测）
      let isInLeftPanel = false;
      if (showLeftPanel) {
        const leftPanelElement = document.querySelector('.fixed.left-0.z-50');
        if (leftPanelElement) {
          const rect = leftPanelElement.getBoundingClientRect();
          isInLeftPanel = e.clientX >= rect.left && e.clientX <= rect.right && 
                          e.clientY >= rect.top && e.clientY <= rect.bottom;
        }
      }
      
      // 左侧面板显示/隐藏逻辑
      const shouldShowLeftPanel = isNearLeft || isInLeftPanel;
      if (shouldShowLeftPanel) {
        setShowLeftPanel(true);
        if (hideLeftPanelTimer) {
          clearTimeout(hideLeftPanelTimer);
          setHideLeftPanelTimer(null);
        }
      } else if (showLeftPanel && !hideLeftPanelTimer) {
        const timer = setTimeout(() => {
          setShowLeftPanel(false);
          setHideLeftPanelTimer(null);
        }, HIDE_DELAY);
        setHideLeftPanelTimer(timer);
      }
    };

    // 添加全局鼠标移动监听器
    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      // 清理定时器
      if (hideTopBarTimer) {
        clearTimeout(hideTopBarTimer);
      }
      if (hideLeftPanelTimer) {
        clearTimeout(hideLeftPanelTimer);
      }
    };
  }, [isFullscreen, showTopBar, showLeftPanel, hideTopBarTimer, hideLeftPanelTimer]);

  // 快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框等元素中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }
      
      // F键切换全屏
      if (e.key === 'f' || e.key === 'F') {
        e.preventDefault();
        setIsFullscreen(prev => !prev);
        return;
      }
      
      // 箭头键调节声呐量程
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        // 增加量程一档（模式+1），但不超过最大值26
        const newMode = Math.min(currentSonarMode + 1, 26);
        if (newMode !== currentSonarMode && sonarRangeRef.current) {
          sonarRangeRef.current.updateSonarMode(newMode);
        }
        return;
      }
      
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        // 降低量程一档（模式-1），但不低于最小值7
        const newMode = Math.max(currentSonarMode - 1, 7);
        if (newMode !== currentSonarMode && sonarRangeRef.current) {
          sonarRangeRef.current.updateSonarMode(newMode);
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSonarMode]);

  return (
    <div className="h-screen tech-blue-bg-gradient box-border relative overflow-hidden font-[family-name:var(--font-family-tech-mono)]">
      {/* 统一的顶部栏 - 通过CSS控制普通模式和全屏模式的位置 */}
      <div className={`transition-all duration-500 ease-in-out ${
        isFullscreen 
          ? `fixed top-0 left-0 right-0 z-60 transition-transform duration-300 ease-out ${showTopBar ? 'translate-y-0' : '-translate-y-full'}`
          : "h-12 translate-y-0 relative z-50"
      }`}>
        <TopBrandBar 
          sonarIP={sonarIP} 
          isFullscreen={isFullscreen}
          onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
        />
      </div>
      
      {/* 主布局容器 */}
      <div className={`flex transition-all duration-500 ease-in-out ${isFullscreen ? "h-screen" : "h-[calc(100vh-48px)]"} ${isFullscreen ? "p-0 gap-0" : "pb-5 pl-0.5 pr-2 pt-2 gap-3"} `}>
        
        {/* 统一的左侧控制面板 - 通过CSS控制普通模式和全屏模式的位置 */}
        <div className={`flex h-full flex-col gap-1.5 bg-tech-blue-panel backdrop-blur-xl rounded-lg tech-blue-card-shadow border border-tech-blue-border transition-all duration-500 ease-in-out overflow-hidden ${
          isFullscreen 
            ? `fixed left-0 z-50 w-24 transition-all duration-300 ease-out ${showLeftPanel ? 'translate-x-0' : '-translate-x-full'} m-2`
            : "w-26 translate-x-0"
        }`}
        style={isFullscreen ? {
          top: showTopBar ? '60px' : '0px',
          height: showTopBar ? 'calc(100vh - 60px - 16px)' : 'calc(100vh - 16px)'
        } : {}}>
          {/* SonarRangeVertical - 进度条区域 */}
          <div className="flex-1 h-full relative overflow-hidden">
            <div className="flex-1 h-full flex items-center justify-center pt-5 relative">
              <SonarRangeVertical 
                ref={sonarRangeRef}
                sonarIP={sonarIP} 
                onModeChange={handleSonarModeChange}
              />
            </div>
          </div>
          
          {/* SonarControl - 控制按钮区域 */}
          <div className="p-4 tech-blue-transition">
            <div className="flex justify-center">
              <SonarControl sonarIP={sonarIP} advancedMode={false} />
            </div>
          </div>
        </div>
        
        {/* SonarImage 主显示区域 */}
        <div className={`flex-1 relative transition-all duration-500 ease-in-out ${isFullscreen ? "fixed top-0 right-0 bottom-0 left-1 z-40" : ""}`}>
          {/* 科技面板容器 - 全屏时保持科技风格 */}
          <div className={`h-full relative transition-all duration-500 ease-in-out ${isFullscreen ? "rounded-none p-4" : "rounded-lg p-0"}`}>
            
            {/* 科技装饰线条 */}
            <div className="absolute top-0 left-[20%] right-[20%] h-px bg-gradient-to-r from-transparent via-tech-blue-border/80 to-transparent tech-blue-glow"></div>
            <div className="absolute bottom-0 left-[20%] right-[20%] h-px bg-gradient-to-r from-transparent via-tech-blue-border/80 to-transparent tech-blue-glow"></div>
            
            {/* 四角指示器 */}
            <div className="absolute top-2 left-2 w-5 h-5 border-t-2 border-l-2 border-tech-blue-border/60 rounded-tl"></div>
            <div className="absolute top-2 right-2 w-5 h-5 border-t-2 border-r-2 border-tech-blue-border/60 rounded-tr"></div>
            <div className="absolute bottom-2 left-2 w-5 h-5 border-b-2 border-l-2 border-tech-blue-border/60 rounded-bl"></div>
            <div className="absolute bottom-2 right-2 w-5 h-5 border-b-2 border-r-2 border-tech-blue-border/60 rounded-br"></div>
            
            
            {/* SonarImage 容器 */}
            <div className={`h-full bg-black relative overflow-hidden p-2 border-2 border-tech-blue-border/60 shadow-[0_0_30px_rgba(59,130,246,0.4),inset_0_0_20px_rgba(59,130,246,0.15)] tech-blue-glow-strong transition-all duration-500 ease-in-out rounded-lg`}>
              
              {/* 渐变边框效果 - 增强强度和光晕范围 */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-tech-blue-border/80 via-tech-blue-border/60 to-tech-blue-border/80 p-[2px] tech-blue-glow-strong">
                <div className="h-full bg-black rounded-lg shadow-[inset_0_0_20px_rgba(59,130,246,0.3)]"></div>
              </div>
              
              {/* 角落装饰渐变 - 加粗视觉效果 */}
              <div className="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-tech-blue-panel/50 via-tech-blue-panel/25 to-transparent rounded-tl-lg z-10 shadow-[0_0_15px_rgba(59,130,246,0.2)]"></div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-tech-blue-panel/50 via-tech-blue-panel/25 to-transparent rounded-tr-lg z-10 shadow-[0_0_15px_rgba(59,130,246,0.2)]"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-tech-blue-panel/50 via-tech-blue-panel/25 to-transparent rounded-bl-lg z-10 shadow-[0_0_15px_rgba(59,130,246,0.2)]"></div>
              <div className="absolute bottom-0 right-0 w-20 h-20 bg-gradient-to-tl from-tech-blue-panel/50 via-tech-blue-panel/25 to-transparent rounded-br-lg z-10 shadow-[0_0_15px_rgba(59,130,246,0.2)]"></div>
                            
              {/* SonarImage 区域 */}
              <div className="h-full bg-black rounded-lg relative z-20 overflow-hidden">
                {/* SonarImage 组件 - 保持单一实例 */}
                <SonarImage
                  sonarIP={sonarIP}
                  theme="dark"
                  autoStart={false}
                  onConnect={handleConnect}
                  onDisconnect={handleDisconnect}
                  onError={handleError}
                  overlayConfig={{
                    showFrameCount: true,
                    showTimestamp: true,
                    showSonarParams: true
                  }}
                  enableMeasurement={true}
                  measurementConfig={{
                    enabled: true,
                    originConfig: {
                      preset: 'bottom-center',
                    },
                    sonarAngle: 60,
                    sonarDirection: 270,
                    showOriginMarker: false,
                    showSectorOutline: false,
                    coordinateFormat: 'polar',
                    precision: {
                      distance: 2,
                      angle: 1,
                    },
                  }}
                  scaleConfig={{
                    mode: 'fit',
                    allowModeToggle: true,
                  }}
                  // onMetaData={(metadata) => console.log("收到元数据:", metadata)}
                  // debug={true}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
