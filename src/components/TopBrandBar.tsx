import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Maximize, Minimize, FileText, HelpCircle } from 'lucide-react';
import SonarDeviceInfo from '../../sonar-react-components/src/components/SonarDeviceInfo';
import IPSettingModal from '../../sonar-react-components/src/components/IPSettingModal';
import type { DeviceInfo } from '../../sonar-react-components/src/components/SonarDeviceInfo';

interface TopBrandBarProps {
  sonarIP: string;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

const TopBrandBar: React.FC<TopBrandBarProps> = ({ sonarIP, isFullscreen = false, onToggleFullscreen }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isIPModalOpen, setIsIPModalOpen] = useState(false);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const handleDeviceInfoChange = (info: DeviceInfo | null) => {
    setDeviceInfo(info);
  };

  const handleError = (error: string) => {
    console.error('SonarDeviceInfo Error:', error);
  };

  return (
    <>
      <div className="flex justify-between items-center h-12 bg-gray-900 border-b border-gray-700 px-4">
        <div className="flex items-center">
          <div className="bg-slate-900/80 backdrop-blur-sm rounded-lg px-1.5 py-1 flex items-center border-2 border-blue-500/40" 
              style={{ boxShadow: '0 0 14px rgba(59, 130, 246, 0.5)' }}>
            <a href="https://www.hanjie-tech.cn" target="_blank" rel="noopener noreferrer">
              <img
                src="/hj-logo.png"
                alt="瀚界 Logo"
                className="h-7 w-7 object-cover object-center cursor-pointer hover:opacity-80 transition-opacity"
                style={{ filter: 'brightness(2.5) contrast(1.3) drop-shadow(0 0 4px rgba(59, 130, 246, 0.8))' }}
              />
            </a>
          </div>
          <h1 className="ml-3 text-white text-xl font-bold">
            <a href="https://hanjie-tech.cn/elementor-%e9%a1%b5%e9%9d%a2-1829/" target="_blank" rel="noopener noreferrer" className="hover:text-blue-300 transition-colors">
              瀚界 C900 SE2 图像声呐
            </a>
            {deviceInfo && (
              <span className="ml-3 text-sm text-blue-400 font-normal">
                [设备编号: {deviceInfo.deviceId}]
              </span>
            )}
          </h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-white text-sm">
            {formatTime(currentTime)}
          </span>
          <button
            onClick={() => setIsHelpModalOpen(true)}
            className="text-gray-400 hover:text-white transition-colors p-1 flex items-center justify-center"
            title="帮助说明"
          >
            <HelpCircle size={20} />
          </button>
          <button
            onClick={() => window.open('/real-time-log.html', '_blank')}
            className="text-gray-400 hover:text-white transition-colors p-1 flex items-center justify-center"
            title="打开实时日志"
          >
            <FileText size={20} />
          </button>
          {onToggleFullscreen && (
            <button
              onClick={onToggleFullscreen}
              className="text-gray-400 hover:text-white transition-colors p-1 flex items-center justify-center"
              title={isFullscreen ? "退出全屏 (F)" : "进入全屏 (F)"}
            >
              {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
            </button>
          )}
          <SonarDeviceInfo
            sonarIP={sonarIP}
            onDeviceInfoChange={handleDeviceInfoChange}
            onError={handleError}
            showIPSetting={true}
            onIPSettingClick={() => setIsIPModalOpen(true)}
          />
        </div>
      </div>
      
      <IPSettingModal 
        sonarIP={sonarIP}
        isOpen={isIPModalOpen}
        onClose={() => setIsIPModalOpen(false)}
      />
      
      {/* 帮助说明模态框 - 使用 Portal 渲染到根 DOM */}
      {isHelpModalOpen && createPortal(
        <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-end justify-end z-[100]">
          <div className="bg-gray-900/90 border border-gray-700 rounded-lg p-6 max-w-sm w-full mx-4 mb-4 shadow-2xl backdrop-blur-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-white text-lg font-bold">快捷键说明</h2>
              <button
                onClick={() => setIsHelpModalOpen(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
                title="关闭"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center text-gray-300">
                <span>切换全屏模式</span>
                <kbd className="bg-gray-700 text-white px-2 py-1 rounded text-xs font-mono">F</kbd>
              </div>
              
              <div className="flex justify-between items-center text-gray-300">
                <span>切换连接状态</span>
                <kbd className="bg-gray-700 text-white px-2 py-1 rounded text-xs font-mono">空格</kbd>
              </div>
              
              <div className="flex justify-between items-center text-gray-300">
                <span>增加声呐量程</span>
                <kbd className="bg-gray-700 text-white px-2 py-1 rounded text-xs font-mono">↑</kbd>
              </div>
              
              <div className="flex justify-between items-center text-gray-300">
                <span>降低声呐量程</span>
                <kbd className="bg-gray-700 text-white px-2 py-1 rounded text-xs font-mono">↓</kbd>
              </div>
              
              <div className="border-t border-gray-700 pt-3 mt-4">
                <p className="text-gray-400 text-xs">
                  注：快捷键仅在非输入框状态下生效
                </p>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default TopBrandBar;