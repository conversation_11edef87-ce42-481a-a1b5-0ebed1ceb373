@import "tailwindcss";

/* Tech Blue Theme Configuration for Tailwind v4 */
@theme {
  --color-tech-blue-bg: #0f1419;
  --color-tech-blue-primary: #00c6ff;
  --color-tech-blue-secondary: #0072ff;
  --color-tech-blue-text: #ffffff;
  --color-tech-blue-text-secondary: #b0b0b0;
  --color-tech-blue-text-tertiary: #808080;
  --color-tech-blue-border: rgba(0, 150, 255, 0.3);
  --color-tech-blue-panel: rgba(0, 150, 255, 0.1);
  --color-tech-blue-panel-20: rgba(0, 150, 255, 0.2);
  --color-tech-blue-panel-30: rgba(0, 150, 255, 0.3);
  --color-tech-blue-glow: rgba(0, 150, 255, 0.5);
  --color-tech-blue-shadow: rgba(0, 0, 0, 0.3);
  --color-tech-blue-gradient-start: #1a2332;
  
  /* Font families */
  --font-family-tech-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  --font-family-tech-chinese: 'Microsoft YaHei', 'SimHei', sans-serif;
  
  /* Font sizes */
  --font-size-tech-main: 14px;
  --font-size-tech-secondary: 12px;
  --font-size-tech-small: 11px;
  --font-size-tech-status: 10px;
}

/* Custom utility classes */
.tech-blue-bg-gradient {
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
}

.tech-blue-progress-gradient {
  background: linear-gradient(180deg, #00c6ff 0%, #0072ff 100%);
}

.tech-blue-glow {
  box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
}

.tech-blue-glow-strong {
  box-shadow: 0 0 15px rgba(0, 150, 255, 0.6);
}

.tech-blue-glow-intense {
  box-shadow: 0 0 20px rgba(0, 150, 255, 0.8);
}

.tech-blue-text-glow {
  text-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
}

.tech-blue-card-shadow {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tech-blue-transition {
  transition: all 0.3s ease-in-out;
}

/* Animation classes */
.tech-blue-pulse {
  animation: tech-blue-pulse 2s infinite;
}

@keyframes tech-blue-pulse {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.8);
  }
}

/* 科技面板阴影 */
.tech-panel-shadow {
  box-shadow: 
    0 0 20px rgba(0, 150, 255, 0.1),
    inset 0 1px 0 rgba(0, 150, 255, 0.2);
}

/* 内阴影效果 */
.shadow-inner-dark {
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.8);
}